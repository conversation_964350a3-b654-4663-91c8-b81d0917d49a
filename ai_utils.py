import re
import json
import os

def extract_json_from_response(response_text):
    json_match = re.search(r'```json\s*({.*?})\s*```', response_text, re.DOTALL)
    if json_match:
        json_str = json_match.group(1)
        if is_valid_json(json_str):
            return json_str
    try:
        if is_valid_json(response_text):
            return response_text
    except:
        pass
    json_match = re.search(r'({.*})', response_text, re.DOTALL)
    if json_match:
        json_str = json_match.group(0)
        if is_valid_json(json_str):
            return json_str
    return None

def is_valid_json(json_str):
    try:
        json.loads(json_str)
        return True
    except json.JSONDecodeError:
        return False

def read_system_prompt(question_type_dir):
    """
    从指定题型目录的system_prompt.md文件中读取system prompt内容
    
    Args:
        question_type_dir (str): 题型目录路径，如 "types/danxuanti"
        
    Returns:
        str: system prompt内容，如果文件不存在则返回None
    """
    system_prompt_file = os.path.join(question_type_dir, "system_prompt.md")
    
    if os.path.exists(system_prompt_file):
        try:
            with open(system_prompt_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                return content if content else None
        except Exception as e:
            print(f"读取system prompt文件失败: {e}")
            return None
    else:
        return None

def ask_user_for_system_prompt():
    """
    询问用户是否要添加system prompt
    
    Returns:
        bool: True表示用户选择添加，False表示不添加
    """
    while True:
        user_input = input("是否要添加system prompt？(y/n): ").strip().lower()
        if user_input in ('y', 'yes', '是'):
            return True
        elif user_input in ('n', 'no', '否'):
            return False
        else:
            print("请输入 y 或 n")

def build_messages_with_system_prompt(user_content, system_prompt=None, include_image=False, base64_image=None, second_image=None):
    """
    构建包含system prompt的messages列表
    
    Args:
        user_content (str): 用户输入的内容
        system_prompt (str): system prompt内容，如果为None则不添加
        include_image (bool): 是否包含图片
        base64_image (str): base64编码的图片数据
        second_image (str): 第二张图片的base64编码数据，用于双图片场景
        
    Returns:
        list: messages列表
    """
    messages = []
    
    # 如果提供了system prompt，添加到messages开头
    if system_prompt:
        messages.append({
            "role": "system",
            "content": system_prompt
        })
    
    # 构建用户消息
    if include_image and base64_image:
        content = [{"type": "text", "text": user_content}]
        
        # 添加第一张图片
        content.append({"type": "image_url", "image_url": {"url": base64_image, "detail": "high"}})
        
        # 如果有第二张图片，添加到内容中
        if second_image:
            content.append({"type": "image_url", "image_url": {"url": second_image, "detail": "high"}})
        
        messages.append({
            "role": "user",
            "content": content
        })
    else:
        messages.append({
            "role": "user",
            "content": user_content
        })
    
    return messages 