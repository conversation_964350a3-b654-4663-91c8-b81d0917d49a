#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import requests
import os
import sys

# 禁用代理
os.environ.pop('HTTP_PROXY', None)
os.environ.pop('HTTPS_PROXY', None)
os.environ.pop('http_proxy', None)
os.environ.pop('https_proxy', None)

# 配置
PROXY_CONFIG = {
    "base_url": "http://14.103.165.110:12000/v1/chat/completions",
    "api_key": "sk-zR4LZYvBUvYj6jYf4TvaYAeXBRxtr7U0EJFzB87NQNw81DAj",
    "model": "doubao-seed-1-6-250615"
}

def test_different_requests():
    """测试不同复杂度的请求"""
    
    test_cases = [
        {
            "name": "极简请求",
            "data": {
                "model": "doubao-seed-1-6-250615",
                "messages": [{"role": "user", "content": "Hi"}],
                "stream": False,
                "max_tokens": 10
            }
        },
        {
            "name": "短请求",
            "data": {
                "model": "doubao-seed-1-6-250615",
                "messages": [{"role": "user", "content": "Hello! Please respond briefly."}],
                "stream": False,
                "max_tokens": 50
            }
        },
        {
            "name": "长请求",
            "data": {
                "model": "doubao-seed-1-6-250615",
                "messages": [{"role": "user", "content": "Please write a detailed explanation about artificial intelligence and its applications in modern technology. Include examples and future prospects."}],
                "stream": False,
                "max_tokens": 500
            }
        }
    ]
    
    with requests.Session() as session:
        session.proxies = {'http': None, 'https': None}
        session.headers.update({
            "Content-Type": "application/json",
            "Authorization": f"Bearer {PROXY_CONFIG['api_key']}"
        })
        
        for test_case in test_cases:
            print(f"\n=== {test_case['name']} ===")
            sys.stdout.flush()
            
            # 测试3次取平均值
            times = []
            for i in range(3):
                start_time = time.time()
                try:
                    response = session.post(
                        PROXY_CONFIG["base_url"],
                        json=test_case['data'],
                        timeout=60
                    )
                    end_time = time.time()
                    request_time = end_time - start_time
                    times.append(request_time)
                    
                    if response.status_code == 200:
                        result = response.json()
                        content = result['choices'][0]['message']['content']
                        print(f"第{i+1}次: {request_time:.2f}秒, 响应长度: {len(content)}字符")
                        print(f"响应内容: {content[:100]}...")
                    else:
                        print(f"第{i+1}次: {request_time:.2f}秒, 错误: {response.status_code}")
                    
                except Exception as e:
                    end_time = time.time()
                    request_time = end_time - start_time
                    print(f"第{i+1}次: {request_time:.2f}秒, 异常: {e}")
                
                sys.stdout.flush()
                time.sleep(0.5)  # 避免请求过快
            
            if times:
                avg_time = sum(times) / len(times)
                print(f"平均响应时间: {avg_time:.2f}秒")
                sys.stdout.flush()

def test_connection_time():
    """测试连接建立时间"""
    print("\n=== 测试连接建立时间 ===")
    sys.stdout.flush()
    
    # 测试不使用session（每次建立新连接）
    print("不使用session（每次新连接）:")
    for i in range(3):
        start_time = time.time()
        try:
            response = requests.post(
                PROXY_CONFIG["base_url"],
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {PROXY_CONFIG['api_key']}"
                },
                json={
                    "model": "doubao-seed-1-6-250615",
                    "messages": [{"role": "user", "content": "Hi"}],
                    "stream": False,
                    "max_tokens": 10
                },
                timeout=60,
                proxies={'http': None, 'https': None}
            )
            end_time = time.time()
            print(f"第{i+1}次: {end_time - start_time:.2f}秒")
        except Exception as e:
            end_time = time.time()
            print(f"第{i+1}次: {end_time - start_time:.2f}秒, 异常: {e}")
        sys.stdout.flush()
        time.sleep(0.5)
    
    # 测试使用session（连接复用）
    print("\n使用session（连接复用）:")
    with requests.Session() as session:
        session.proxies = {'http': None, 'https': None}
        session.headers.update({
            "Content-Type": "application/json",
            "Authorization": f"Bearer {PROXY_CONFIG['api_key']}"
        })
        
        for i in range(3):
            start_time = time.time()
            try:
                response = session.post(
                    PROXY_CONFIG["base_url"],
                    json={
                        "model": "doubao-seed-1-6-250615",
                        "messages": [{"role": "user", "content": "Hi"}],
                        "stream": False,
                        "max_tokens": 10
                    },
                    timeout=60
                )
                end_time = time.time()
                print(f"第{i+1}次: {end_time - start_time:.2f}秒")
            except Exception as e:
                end_time = time.time()
                print(f"第{i+1}次: {end_time - start_time:.2f}秒, 异常: {e}")
            sys.stdout.flush()
            time.sleep(0.5)

if __name__ == "__main__":
    print("开始分析延迟原因...")
    sys.stdout.flush()
    
    test_different_requests()
    test_connection_time()
    
    print("\n分析完成！")
    sys.stdout.flush()
