#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import httpx
import requests
import json

# 配置
PROXY_CONFIG = {
    "base_url": "http://14.103.165.110:12000/v1/chat/completions",
    "api_key": "sk-zR4LZYvBUvYj6jYf4TvaYAeXBRxtr7U0EJFzB87NQNw81DAj",
    "model": "doubao-seed-1-6-250615"
}

# 创建HTTP客户端
client = httpx.Client(
    timeout=httpx.Timeout(connect=5, read=120, write=30, pool=10)
)

def test_simple_request():
    """测试简单请求（类似curl）"""
    print("=== 测试简单请求 ===")

    request_data = {
        "model": "doubao-seed-1-6-250615",
        "messages": [{"role": "user", "content": "Hello!"}],
        "stream": False
    }

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {PROXY_CONFIG['api_key']}",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }
    
    try:
        response = client.post(
            PROXY_CONFIG["base_url"],
            headers=headers,
            json=request_data
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 简单请求成功")
            result = response.json()
            print(f"响应: {result['choices'][0]['message']['content'][:100]}...")
        else:
            print(f"❌ 简单请求失败: {response.text}")
    except Exception as e:
        print(f"❌ 简单请求异常: {e}")

def test_complex_request():
    """测试复杂请求（包含额外参数）"""
    print("\n=== 测试复杂请求 ===")
    
    request_data = {
        "model": "doubao-seed-1-6-250615",
        "messages": [{"role": "user", "content": "Hello!"}],
        "stream": False,
        "temperature": 1.0,
        "top_p": 0.7,
        "max_tokens": 1000,
        "thinking": {"type": "disabled"}  # 这个参数可能有问题
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {PROXY_CONFIG['api_key']}",
        "x-is-encrypted": "true"  # 这个头可能有问题
    }
    
    try:
        response = client.post(
            PROXY_CONFIG["base_url"],
            headers=headers,
            json=request_data
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 复杂请求成功")
            result = response.json()
            print(f"响应: {result['choices'][0]['message']['content'][:100]}...")
        else:
            print(f"❌ 复杂请求失败: {response.text}")
    except Exception as e:
        print(f"❌ 复杂请求异常: {e}")

def test_without_extra_params():
    """测试去掉可疑参数的请求"""
    print("\n=== 测试去掉可疑参数的请求 ===")
    
    request_data = {
        "model": "doubao-seed-1-6-250615",
        "messages": [{"role": "user", "content": "Hello!"}],
        "stream": False,
        "temperature": 1.0,
        "top_p": 0.7,
        "max_tokens": 1000
        # 去掉 thinking 参数
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {PROXY_CONFIG['api_key']}"
        # 去掉 x-is-encrypted 头
    }
    
    try:
        response = client.post(
            PROXY_CONFIG["base_url"],
            headers=headers,
            json=request_data
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 去掉可疑参数的请求成功")
            result = response.json()
            print(f"响应: {result['choices'][0]['message']['content'][:100]}...")
        else:
            print(f"❌ 去掉可疑参数的请求失败: {response.text}")
    except Exception as e:
        print(f"❌ 去掉可疑参数的请求异常: {e}")

def test_with_requests():
    """使用requests库测试"""
    print("\n=== 使用requests库测试 ===")

    request_data = {
        "model": "doubao-seed-1-6-250615",
        "messages": [{"role": "user", "content": "Hello!"}],
        "stream": False
    }

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {PROXY_CONFIG['api_key']}"
    }

    try:
        response = requests.post(
            PROXY_CONFIG["base_url"],
            headers=headers,
            json=request_data,
            timeout=30
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ requests请求成功")
            result = response.json()
            print(f"响应: {result['choices'][0]['message']['content'][:100]}...")
        else:
            print(f"❌ requests请求失败: {response.text}")
    except Exception as e:
        print(f"❌ requests请求异常: {e}")

if __name__ == "__main__":
    test_simple_request()
    test_complex_request()
    test_without_extra_params()
    test_with_requests()
    client.close()
