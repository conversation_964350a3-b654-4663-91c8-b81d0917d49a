#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包视觉定位 Grounding 功能批量测试脚本

功能说明：
1. 让用户选择题型（和test.py一致）
2. 让用户选择图片处理方式：
   - 白噪点：在框外添加80%密度的白色像素点（模拟马赛克）
   - 高斯模糊：对框外背景轻度模糊（radius=5-10），保留轮廓但弱化细节
   - 半透明蒙版：将框外区域覆盖50%透明度的黑色图层（类似手机截图标注效果）
   - 仅画框：除了画框外，其他区域不做任何处理
3. 从相应题型的 images 文件夹中读取所有图片
4. 从相应题型的 grounding_prompt.md 文件中读取提示词
5. 调用豆包 API 进行视觉定位
6. 解析返回的边界框坐标
7. 在图片上绘制边界框并应用选择的处理方式
8. 将处理后的图片保存到相应题型的grounding_result文件夹中

使用方法：
1. 设置环境变量 ARK_API_KEY
2. 运行脚本：python grounding_test.py
3. 选择题型
4. 选择图片处理方式
5. 脚本会自动处理相应题型下的所有图片
"""

import os
import base64
import cv2
import datetime
import shutil
import markdown
from bs4 import BeautifulSoup
from volcenginesdkarkruntime import Ark
from pypinyin import pinyin, Style
import multiprocessing  # 新增：导入多进程模块
import time  # 新增：用于记录时间
import numpy as np  # 新增：用于图像处理
import random  # 新增：用于白噪点生成

# 尝试导入cv2，如果失败则设置标志
try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False
    print("警告：未安装 opencv-python，将跳过边界框绘制功能")

# 配置参数
DEFAULT_MODEL = "doubao-seed-1-6-250615"  # 豆包模型ID
BBOX_TAG_START = "<bbox>"
BBOX_TAG_END = "</bbox>"

def chinese_to_pinyin(chinese_text):
    """将中文转换为拼音（无声调）"""
    pinyin_list = pinyin(chinese_text, style=Style.NORMAL)
    return ''.join([p[0] for p in pinyin_list])

def get_model_choice():
    """让用户选择模型ID"""
    available_models = {
        "1": "doubao-seed-1-6-250615",
        "2": "doubao-seed-1-6-flash-250715",
        "3": "doubao-1-5-thinking-vision-pro-250428",
        "4": "doubao-1-5-vision-pro-32k-250115"
    }

    print("请选择模型ID：")
    for key, value in available_models.items():
        print(f"{key}. {value}")

    while True:
        user_input = input("请输入模型编号（1-4）：").strip()
        if user_input in available_models:
            selected_model = available_models[user_input]
            print(f"选择的模型：{selected_model}")
            return selected_model
        else:
            print("输入无效，请输入 1-4 的数字")

def get_question_type():
    """获取用户输入的题型并转换为拼音路径（和test.py一致）"""
    question_types = {
        "1": "涂卡选择题",
        "2": "涂卡判断题",
        "3": "连线题",
        "4": "图表题",
        "5": "翻译题",
        "6": "画图题",
        "7": "数学应用题",
        "8": "数学计算题",
        "9": "简单的四则运算",
        "10": "填空题",
        "11": "判断题",
        "12": "多选题",
        "13": "单选题"
    }

    print("请选择题型：")
    for key, value in question_types.items():
        print(f"{key}. {value}")

    while True:
        user_input = input("请输入题型编号（1-13）或直接输入中文：").strip()

        # 检查数字输入
        if user_input in question_types:
            question_type = question_types[user_input]
            break
        # 检查中文输入
        elif user_input in question_types.values():
            question_type = user_input
            break
        else:
            print("输入无效，请输入 1-13 的数字或直接输入中文题型名称")

    # 转换为拼音
    pinyin_name = chinese_to_pinyin(question_type)
    print(f"选择的题型：{question_type}")
    print(f"对应的拼音路径：{pinyin_name}")

    return question_type, pinyin_name

def get_image_processing_type():
    """获取用户选择的图片处理方式"""
    processing_types = {
        "1": "白噪点",
        "2": "高斯模糊",
        "3": "半透明蒙版",
        "4": "仅画框"
    }

    print("\n请选择图片处理方式：")
    print("1. 白噪点：在框外随机添加80%密度的白色像素点（模拟马赛克）")
    print("2. 高斯模糊：对框外背景轻度模糊（radius=5-10），保留轮廓但弱化细节")
    print("3. 半透明蒙版：将框外区域覆盖50%透明度的黑色图层（类似手机截图标注效果）")
    print("4. 仅画框：除了画框外，其他区域不做任何处理")

    while True:
        user_input = input("请输入处理方式编号（1-4）：").strip()

        if user_input in processing_types:
            processing_type = processing_types[user_input]
            print(f"选择的处理方式：{processing_type}")
            return user_input, processing_type
        else:
            print("输入无效，请输入 1-4 的数字")

def markdown_to_text(markdown_content):
    """将markdown格式转换为纯文本"""
    # 将markdown转换为HTML
    html = markdown.markdown(markdown_content)
    # 使用BeautifulSoup提取纯文本
    soup = BeautifulSoup(html, 'html.parser')
    # 获取纯文本内容
    text = soup.get_text()
    # 清理多余的空白字符
    import re
    text = re.sub(r'\n\s*\n', '\n\n', text)  # 将多个空行替换为两个换行
    text = re.sub(r'[ \t]+', ' ', text)  # 将多个空格替换为单个空格
    return text.strip()

def get_image_files(images_dir):
    """获取images文件夹中的所有图片文件，按文件名字典序排序"""
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
    image_files = []
    if os.path.exists(images_dir):
        for filename in os.listdir(images_dir):
            if any(filename.lower().endswith(ext) for ext in image_extensions):
                image_files.append(os.path.join(images_dir, filename))
    # 按文件名字典序排序
    return sorted(image_files)

def image_to_base64(image_path):
    """将图片文件转换为base64编码"""
    with open(image_path, "rb") as image_file:
        image_data = image_file.read()

    # 根据文件扩展名确定MIME类型
    ext = os.path.splitext(image_path)[1].lower()
    if ext in ['.jpg', '.jpeg']:
        mime_type = 'image/jpeg'
    elif ext == '.png':
        mime_type = 'image/png'
    elif ext == '.gif':
        mime_type = 'image/gif'
    elif ext == '.webp':
        mime_type = 'image/webp'
    elif ext == '.bmp':
        mime_type = 'image/bmp'
    else:
        mime_type = 'image/jpeg'  # 默认使用jpeg

    encoded_string = base64.b64encode(image_data).decode('utf-8')
    return f"data:{mime_type};base64,{encoded_string}"

def process_single_image_api(task):
    """
    只做API推理，输入为task字典（包含base64、图片名、prompt等）
    """
    from volcenginesdkarkruntime import Ark
    import os
    img_filename = task['img_filename']
    base64_image = task['base64_image']
    user_prompt = task['user_prompt']
    client_api_key = task['client_api_key']
    model_id = task.get('model_id', 'doubao-seed-1-6-250615')  # 支持模型参数
    index = task['index']
    image_path_prefix = task['image_path_prefix']
    sep = f"\n{'=' * 50}\n"
    info = f"处理第 {index} 张图片: {img_filename}"
    current_image_output_lines = []
    current_image_output_lines.append(sep)
    current_image_output_lines.append(info + "\n")
    current_image_output_lines.append(sep)
    # 生成标注后的图片文件名
    bbox_filename = f"{os.path.splitext(img_filename)[0]}_with_bbox{os.path.splitext(img_filename)[1]}"
    current_image_output_lines.append(f"![{bbox_filename}]({image_path_prefix}{img_filename})\n")
    print(f"[PID {os.getpid()}] 处理第 {index} 张图片: {img_filename}")
    try:
        client_local = Ark(
            base_url="https://ark.cn-beijing.volces.com/api/v3",
            api_key=client_api_key,
        )
        # 记录开始时间
        start_time = time.time()
        
        # 使用新的messages构建函数，支持system prompt
        messages = build_messages_with_system_prompt(
            user_content=user_prompt,
            system_prompt=task.get('system_prompt'),
            include_image=True,
            base64_image=base64_image
        )
        
        response = client_local.chat.completions.create(
            model=model_id,
            messages=messages,
            extra_headers={'x-is-encrypted': 'true'},
            temperature=0.1,
            top_p=0.7,
            max_tokens=4096,
        )
        # 记录结束时间并计算响应时间
        end_time = time.time()
        response_time = end_time - start_time
        resp_content = response.choices[0].message.content.strip()
        current_image_output_lines.append(f"### Grounding响应内容：\n")
        current_image_output_lines.append("```\n")
        current_image_output_lines.append(f"{resp_content}\n")
        current_image_output_lines.append("```\n")
        # 添加响应时间记录
        current_image_output_lines.append(f"### 响应时间：{response_time:.2f}秒\n")
        # token用量信息
        usage = getattr(response, 'usage', None)
        total_tokens = usage.total_tokens if usage and hasattr(usage, 'total_tokens') else None
        cached_tokens = None
        reasoning_tokens = None
        if usage:
            if hasattr(usage, 'prompt_tokens_details') and usage.prompt_tokens_details:
                cached_tokens = getattr(usage.prompt_tokens_details, 'cached_tokens', None)
            if hasattr(usage, 'completion_tokens_details') and usage.completion_tokens_details:
                reasoning_tokens = getattr(usage.completion_tokens_details, 'reasoning_tokens', None)
        current_image_output_lines.append("### token用量\n")
        current_image_output_lines.append(f"- total_tokens: {total_tokens}\n")
        current_image_output_lines.append(f"- cached_tokens: {cached_tokens}\n")
        current_image_output_lines.append(f"- reasoning_tokens: {reasoning_tokens}\n")
        return {
            'success': True,
            'image_path': task['image_path'],
            'output_lines': current_image_output_lines,
            'response_content': resp_content
        }
    except Exception as e:
        err_msg = f"处理图片 {img_filename} 时出错: {str(e)}"
        print(f"[PID {os.getpid()}] 处理图片 {img_filename} 时出错: {str(e)}")
        current_image_output_lines.append(err_msg + "\n")
        return {
            'success': False,
            'image_path': task['image_path'],
            'output_lines': current_image_output_lines,
            'response_content': None
        }

def parse_bbox_coordinates(bbox_content):
    """解析边界框坐标，支持多个边界框"""
    import re

    # 查找所有的bbox标签
    bbox_pattern = r'<bbox>([^<]+)</bbox>'
    matches = re.findall(bbox_pattern, bbox_content)

    if not matches:
        raise ValueError("未找到有效的边界框标签")

    all_coords = []
    for match in matches:
        coords_str = match.strip()
        try:
            coords = list(map(int, coords_str.split()))
            if len(coords) != 4:  # 验证坐标数量(xmin, ymin, xmax, ymax)
                raise ValueError(f"坐标数量不正确，需要4个数值，得到: {coords}")
            all_coords.append(coords)
        except ValueError as e:
            print(f"警告：解析坐标时出错: {e}, 跳过此坐标: {coords_str}")
            continue

    if not all_coords:
        raise ValueError("没有找到有效的坐标")

    return all_coords

def create_bbox_mask(image_shape, all_coords):
    """创建边界框区域的掩码，返回框内为True，框外为False的掩码"""
    h, w = image_shape[:2]
    mask = np.zeros((h, w), dtype=bool)

    for coords in all_coords:
        x_min, y_min, x_max, y_max = coords
        # 缩放坐标(模型输出范围为0-1000)
        x_min_real = int(x_min * w / 1000)
        y_min_real = int(y_min * h / 1000)
        x_max_real = int(x_max * w / 1000)
        y_max_real = int(y_max * h / 1000)

        # 确保坐标在图像范围内，并且max坐标不能小于min坐标
        x_min_real = max(0, min(x_min_real, w-1))
        y_min_real = max(0, min(y_min_real, h-1))
        x_max_real = max(x_min_real, min(x_max_real, w-1))
        y_max_real = max(y_min_real, min(y_max_real, h-1))

        # 在掩码中标记边界框区域（确保有效的切片范围）
        if x_max_real > x_min_real and y_max_real > y_min_real:
            mask[y_min_real:y_max_real+1, x_min_real:x_max_real+1] = True

    return mask

def apply_white_noise_processing(image, bbox_mask):
    """应用白噪点处理：在框外区域随机添加10%-20%密度的白色像素点"""
    processed_image = image.copy()

    # 创建框外区域掩码
    outside_mask = ~bbox_mask

    # 调试信息
    total_pixels = bbox_mask.size
    bbox_pixels = np.sum(bbox_mask)
    outside_pixels = np.sum(outside_mask)
    print(f"  [调试] 总像素: {total_pixels}, 框内像素: {bbox_pixels}, 框外像素: {outside_pixels}")

    # 检查原图框内的白色像素数量
    original_bbox_region = image[bbox_mask]
    original_white_in_bbox = np.sum(np.all(original_bbox_region == [255, 255, 255], axis=1))
    print(f"  [调试] 原图框内白色像素: {original_white_in_bbox}")

    # 获取框外区域的像素位置
    outside_positions = np.where(outside_mask)
    total_outside_pixels = len(outside_positions[0])

    if total_outside_pixels > 0:
        # 固定选择80%的框外像素添加白噪点
        noise_density = 0.8
        num_noise_pixels = int(total_outside_pixels * noise_density)

        print(f"  [调试] 将添加 {num_noise_pixels} 个白噪点到框外区域")

        # 随机选择像素位置
        indices = random.sample(range(total_outside_pixels), num_noise_pixels)
        noise_y = outside_positions[0][indices]
        noise_x = outside_positions[1][indices]

        # 添加白色噪点（只在框外区域）
        processed_image[noise_y, noise_x] = [255, 255, 255]

        # 验证：检查处理后框内的白色像素数量
        processed_bbox_region = processed_image[bbox_mask]
        processed_white_in_bbox = np.sum(np.all(processed_bbox_region == [255, 255, 255], axis=1))

        if processed_white_in_bbox > original_white_in_bbox:
            print(f"  [警告] 框内新增了 {processed_white_in_bbox - original_white_in_bbox} 个白色像素！")
        elif processed_white_in_bbox == original_white_in_bbox:
            print(f"  [正常] 框内白色像素数量未变化，白噪点只在框外")
        else:
            print(f"  [异常] 框内白色像素减少了？这不应该发生")

    return processed_image

def apply_gaussian_blur_processing(image, bbox_mask):
    """应用高斯模糊处理：对框外背景轻度模糊（radius=5-10）"""
    processed_image = image.copy()

    # 随机选择模糊半径
    blur_radius = random.randint(5, 10)
    kernel_size = blur_radius * 2 + 1

    # 对整个图像应用高斯模糊
    blurred_image = cv2.GaussianBlur(image, (kernel_size, kernel_size), 0)

    # 只在框外区域应用模糊效果
    outside_mask = ~bbox_mask
    processed_image[outside_mask] = blurred_image[outside_mask]

    return processed_image

def apply_transparent_mask_processing(image, bbox_mask):
    """应用半透明蒙版处理：将框外区域覆盖50%透明度的黑色图层"""
    processed_image = image.copy().astype(np.float32)

    # 固定透明度和蒙版颜色
    alpha = 0.5  # 50%透明度
    mask_color = 0  # 黑色

    # 创建框外区域掩码
    outside_mask = ~bbox_mask

    # 应用半透明蒙版
    processed_image[outside_mask] = processed_image[outside_mask] * (1 - alpha) + mask_color * alpha

    return processed_image.astype(np.uint8)

def draw_bbox_on_image(image_path, all_coords, output_path, processing_type="4"):
    """在图片上绘制多个边界框并根据处理类型应用不同的图像处理效果"""
    # 读取原图
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"无法读取图片: {image_path}")

    # 获取图像尺寸
    h, w = image.shape[:2]

    # 创建边界框掩码
    bbox_mask = create_bbox_mask(image.shape, all_coords)

    # 根据处理类型应用不同的图像处理
    if processing_type == "1":  # 白噪点
        processed_image = apply_white_noise_processing(image, bbox_mask)
    elif processing_type == "2":  # 高斯模糊
        processed_image = apply_gaussian_blur_processing(image, bbox_mask)
    elif processing_type == "3":  # 半透明蒙版
        processed_image = apply_transparent_mask_processing(image, bbox_mask)
    else:  # 仅画框（默认）
        processed_image = image.copy()

    # 定义不同颜色用于区分不同的边界框
    colors = [
        (0, 0, 255),    # 红色
        (0, 255, 0),    # 绿色
        (255, 0, 0),    # 蓝色
        (0, 255, 255),  # 黄色
        (255, 0, 255),  # 紫色
        (255, 255, 0),  # 青色
        (128, 0, 128),  # 紫色
        (255, 165, 0),  # 橙色
    ]

    # 绘制所有边界框
    for i, coords in enumerate(all_coords):
        x_min, y_min, x_max, y_max = coords
        # 缩放坐标(模型输出范围为0-1000)
        x_min_real = int(x_min * w / 1000)
        y_min_real = int(y_min * h / 1000)
        x_max_real = int(x_max * w / 1000)
        y_max_real = int(y_max * h / 1000)

        # 选择颜色
        color = colors[i % len(colors)]

        # 绘制边界框
        cv2.rectangle(processed_image, (x_min_real, y_min_real), (x_max_real, y_max_real), color, 3)

        # 添加标签
        label = f"Box{i+1}"
        cv2.putText(processed_image, label, (x_min_real, y_min_real - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)

    # 保存结果图片
    cv2.imwrite(output_path, processed_image)
    return True

def process_grounding_result(result, output_dir, processing_type="4"):
    """处理grounding结果，绘制边界框"""
    if not result['success']:
        return {
            'success': False,
            'filename': os.path.basename(result['image_path']),
            'error': 'API调用失败'
        }

    image_path = result['image_path']
    filename = os.path.basename(image_path)
    bbox_content = result['response_content']

    try:
        # 解析坐标
        all_coords = parse_bbox_coordinates(bbox_content)
        print(f"[{filename}] 解析到 {len(all_coords)} 个边界框: {all_coords}")

        # 只保存绘制边界框后的图片（不保存原图）
        bbox_filename = f"{os.path.splitext(filename)[0]}_with_bbox{os.path.splitext(filename)[1]}"
        bbox_output_path = os.path.join(output_dir, bbox_filename)
        draw_bbox_on_image(image_path, all_coords, bbox_output_path, processing_type)

        print(f"✓ 成功处理: {filename}")
        print(f"  - 标注图保存至: {bbox_output_path}")

        return {
            'success': True,
            'filename': filename,
            'coords': all_coords,
            'bbox_count': len(all_coords),
            'bbox_content': bbox_content,
            'bbox_path': bbox_output_path,
            'output_lines': result['output_lines']
        }

    except Exception as e:
        error_msg = f"处理图片 {filename} 时出错: {str(e)}"
        print(f"✗ {error_msg}")
        return {
            'success': False,
            'filename': filename,
            'error': str(e),
            'output_lines': result['output_lines']
        }

def main():
    """主函数"""
    print("=" * 60)
    print("豆包视觉定位 Grounding 功能批量测试")
    print("=" * 60)

    # 获取用户选择的模型
    selected_model = get_model_choice()

    # 获取用户选择的题型
    question_type, pinyin_name = get_question_type()

    # 获取用户选择的图片处理方式
    processing_type_id, processing_type_name = get_image_processing_type()

    # 构建题型相关的路径
    types_dir = "types"
    question_dir = os.path.join(types_dir, pinyin_name)
    images_dir = os.path.join(question_dir, "images")
    grounding_result_dir = os.path.join(question_dir, "grounding_result")
    grounding_prompt_file = os.path.join(question_dir, "grounding_prompt.md")

    print(f"\n使用配置：")
    print(f"题型：{question_type}")
    print(f"图片处理方式：{processing_type_name}")
    print(f"题型目录：{question_dir}")
    print(f"图片文件夹：{images_dir}")
    print(f"结果文件夹：{grounding_result_dir}")
    print(f"提示词文件：{grounding_prompt_file}")

    # 检查并创建必要的目录
    os.makedirs(grounding_result_dir, exist_ok=True)

    # 读取提示词
    if not os.path.exists(grounding_prompt_file):
        print(f"错误：提示词文件 {grounding_prompt_file} 不存在！")
        print(f"请创建 {grounding_prompt_file} 文件并写入提示词内容")
        return

    try:
        with open(grounding_prompt_file, 'r', encoding='utf-8') as f:
            markdown_prompt = f.read().strip()
        prompt = markdown_to_text(markdown_prompt)
        print(f"已读取提示词: {prompt}")
    except Exception as e:
        print(f"读取提示词文件时出错：{str(e)}")
        return

    if not prompt:
        print("错误：提示词文件为空！")
        return

    # 获取图片文件
    image_files = get_image_files(images_dir)
    if not image_files:
        print(f"错误：在 {images_dir} 文件夹中没有找到图片文件！")
        print("支持的格式：.jpg, .jpeg, .png, .gif, .webp, .bmp")
        return

    print(f"找到 {len(image_files)} 张图片")

    # 创建输出目录
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    output_dir = os.path.join(grounding_result_dir, f"images_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")

    # 处理所有图片（多线程处理）
    print("\n--- 开始本地处理图片（编码） ---\n")
    # 主进程完成所有图片的base64编码
    api_key_from_client_init = "36c2aa0e-8b2b-4412-bc92-d3d1cef96b1b"
    tasks = []
    for i, image_path in enumerate(image_files):
        img_filename = os.path.basename(image_path)
        try:
            base64_image = image_to_base64(image_path)
        except Exception as e:
            print(f"图片 {img_filename} 处理失败: {e}")
            base64_image = None
        tasks.append({
            'img_filename': img_filename,
            'base64_image': base64_image,
            'user_prompt': prompt,
            'client_api_key': api_key_from_client_init,
            'model_id': selected_model,  # 添加模型ID参数
            'index': i + 1,
            'image_path_prefix': "../images/",
            'image_path': image_path
        })

    # 过滤掉处理失败的图片
    tasks = [t for t in tasks if t['base64_image'] is not None]
    print(f"\n--- 本地处理完成，开始并行API推理 ---\n")
    num_processes = os.cpu_count() if os.cpu_count() else 4
    print(f"将使用 {num_processes} 个进程进行并行API推理。")

    # 并行API推理
    with multiprocessing.Pool(processes=num_processes) as pool:
        api_results = pool.map(process_single_image_api, tasks)

    print("\n--- 并行API推理完成，开始后处理 ---\n")

    # 后处理：解析坐标并绘制边界框
    results = []
    success_count = 0
    output_lines = []

    for result in api_results:
        processed_result = process_grounding_result(result, output_dir, processing_type_id)
        results.append(processed_result)

        if processed_result['success']:
            success_count += 1

        # 收集输出行用于报告
        if 'output_lines' in processed_result:
            output_lines.extend(processed_result['output_lines'])

    # 生成处理报告
    print("\n" + "=" * 60)
    print("处理完成！")
    print("=" * 60)
    print(f"总计图片: {len(image_files)}")
    print(f"成功处理: {success_count}")
    print(f"失败处理: {len(image_files) - success_count}")
    print(f"成功率: {success_count/len(image_files)*100:.1f}%")
    print(f"结果保存在: {output_dir}")

    # 计算总token数量
    total_tokens_sum = 0
    for line in output_lines:
        if line.startswith("- total_tokens: ") and "None" not in line:
            try:
                token_count = int(line.split(": ")[1].strip())
                total_tokens_sum += token_count
            except:
                pass

    # 生成summary.md报告（仿照test.py格式）
    summary_path = os.path.join(output_dir, "summary.md")
    with open(summary_path, 'w', encoding='utf-8') as f:
        # 头部统计信息
        f.write(f"# 豆包视觉定位 Grounding 测试报告\n\n")
        f.write(f"**使用模型ID**: {selected_model}\n\n")
        f.write(f"**题型**: {question_type}\n\n")
        f.write(f"**图片处理方式**: {processing_type_name}\n\n")
        f.write(f"**运行时间**: {timestamp}\n\n")
        f.write(f"找到 {len(image_files)} 张图片，开始逐个处理...\n")
        f.write(f"使用的提示词: {prompt}\n\n")
        f.write(f"**处理统计**:\n")
        f.write(f"- 总计图片: {len(image_files)}\n")
        f.write(f"- 成功处理: {success_count}\n")
        f.write(f"- 失败处理: {len(image_files) - success_count}\n")
        f.write(f"- 成功率: {success_count/len(image_files)*100:.1f}%\n")
        f.write(f"- 总token消耗: {total_tokens_sum}\n\n")

        # 写入详细的API响应内容（仿照test.py格式）
        for line in output_lines:
            # 修改图片路径引用，因为现在只有标注后的图片
            if line.startswith("![") and "](../images/" in line:
                # 提取文件名
                import re
                match = re.search(r'!\[([^\]]+)\]\(\.\.\/images\/([^)]+)\)', line)
                if match:
                    img_name = match.group(2)
                    bbox_name = f"{os.path.splitext(img_name)[0]}_with_bbox{os.path.splitext(img_name)[1]}"
                    f.write(f"![{bbox_name}]({bbox_name})\n")
                else:
                    f.write(line)
            else:
                f.write(line)

        # 添加结束标记
        f.write("\n" + "=" * 50 + "\n")
        f.write("所有图片处理完成！\n")
        f.write("=" * 50 + "\n")

    print(f"Summary报告保存在: {summary_path}")

if __name__ == "__main__":
    main()
