#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import requests
import httpx
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor
import os

# 禁用代理
os.environ.pop('HTTP_PROXY', None)
os.environ.pop('HTTPS_PROXY', None)
os.environ.pop('http_proxy', None)
os.environ.pop('https_proxy', None)

# 配置
PROXY_CONFIG = {
    "base_url": "http://14.103.165.110:12000/v1/chat/completions",
    "api_key": "sk-zR4LZYvBUvYj6jYf4TvaYAeXBRxtr7U0EJFzB87NQNw81DAj",
    "model": "doubao-seed-1-6-250615"
}

def create_test_request():
    """创建测试请求"""
    return {
        "model": "doubao-seed-1-6-250615",
        "messages": [{"role": "user", "content": "Hello! Please respond briefly."}],
        "stream": False,
        "max_tokens": 50
    }

def test_requests_no_session(num_requests=5):
    """测试requests库不使用session"""
    print(f"\n=== 测试requests库(无session) - {num_requests}个请求 ===", flush=True)

    start_time = time.time()
    
    for i in range(num_requests):
        try:
            response = requests.post(
                PROXY_CONFIG["base_url"],
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {PROXY_CONFIG['api_key']}"
                },
                json=create_test_request(),
                timeout=30,
                proxies={'http': None, 'https': None}
            )
            print(f"请求{i+1}: {response.status_code}")
        except Exception as e:
            print(f"请求{i+1}失败: {e}")
    
    end_time = time.time()
    print(f"总耗时: {end_time - start_time:.2f}秒")
    print(f"平均每请求: {(end_time - start_time)/num_requests:.2f}秒")

def test_requests_with_session(num_requests=5):
    """测试requests库使用session"""
    print(f"\n=== 测试requests库(使用session) - {num_requests}个请求 ===")
    
    start_time = time.time()
    
    with requests.Session() as session:
        session.proxies = {'http': None, 'https': None}
        session.headers.update({
            "Content-Type": "application/json",
            "Authorization": f"Bearer {PROXY_CONFIG['api_key']}"
        })
        
        for i in range(num_requests):
            try:
                response = session.post(
                    PROXY_CONFIG["base_url"],
                    json=create_test_request(),
                    timeout=30
                )
                print(f"请求{i+1}: {response.status_code}")
            except Exception as e:
                print(f"请求{i+1}失败: {e}")
    
    end_time = time.time()
    print(f"总耗时: {end_time - start_time:.2f}秒")
    print(f"平均每请求: {(end_time - start_time)/num_requests:.2f}秒")

def test_httpx_client(num_requests=5):
    """测试httpx客户端"""
    print(f"\n=== 测试httpx客户端 - {num_requests}个请求 ===")
    
    start_time = time.time()
    
    with httpx.Client(
        timeout=httpx.Timeout(connect=5, read=120, write=30, pool=10),
        limits=httpx.Limits(max_connections=100, max_keepalive_connections=20)
    ) as client:
        for i in range(num_requests):
            try:
                response = client.post(
                    PROXY_CONFIG["base_url"],
                    headers={
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {PROXY_CONFIG['api_key']}"
                    },
                    json=create_test_request()
                )
                print(f"请求{i+1}: {response.status_code}")
            except Exception as e:
                print(f"请求{i+1}失败: {e}")
    
    end_time = time.time()
    print(f"总耗时: {end_time - start_time:.2f}秒")
    print(f"平均每请求: {(end_time - start_time)/num_requests:.2f}秒")

def test_parallel_requests(num_requests=5, max_workers=3):
    """测试并行请求"""
    print(f"\n=== 测试并行请求({max_workers}线程) - {num_requests}个请求 ===")
    
    def make_request(i):
        try:
            with httpx.Client(
                timeout=httpx.Timeout(connect=5, read=120, write=30, pool=10)
            ) as client:
                response = client.post(
                    PROXY_CONFIG["base_url"],
                    headers={
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {PROXY_CONFIG['api_key']}"
                    },
                    json=create_test_request()
                )
                print(f"请求{i+1}: {response.status_code}")
                return response.status_code
        except Exception as e:
            print(f"请求{i+1}失败: {e}")
            return None
    
    start_time = time.time()
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = [executor.submit(make_request, i) for i in range(num_requests)]
        results = [future.result() for future in futures]
    
    end_time = time.time()
    print(f"总耗时: {end_time - start_time:.2f}秒")
    print(f"平均每请求: {(end_time - start_time)/num_requests:.2f}秒")
    print(f"成功请求: {sum(1 for r in results if r == 200)}/{num_requests}")

async def test_async_requests(num_requests=5):
    """测试异步请求"""
    print(f"\n=== 测试异步请求 - {num_requests}个请求 ===")
    
    async def make_async_request(session, i):
        try:
            async with session.post(
                PROXY_CONFIG["base_url"],
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {PROXY_CONFIG['api_key']}"
                },
                json=create_test_request()
            ) as response:
                print(f"请求{i+1}: {response.status}")
                return response.status
        except Exception as e:
            print(f"请求{i+1}失败: {e}")
            return None
    
    start_time = time.time()
    
    async with aiohttp.ClientSession() as session:
        tasks = [make_async_request(session, i) for i in range(num_requests)]
        results = await asyncio.gather(*tasks)
    
    end_time = time.time()
    print(f"总耗时: {end_time - start_time:.2f}秒")
    print(f"平均每请求: {(end_time - start_time)/num_requests:.2f}秒")
    print(f"成功请求: {sum(1 for r in results if r == 200)}/{num_requests}")

if __name__ == "__main__":
    num_requests = 10
    
    print("HTTP性能测试开始...")
    
    # 串行测试
    test_requests_no_session(num_requests)
    test_requests_with_session(num_requests)
    test_httpx_client(num_requests)
    
    # 并行测试
    test_parallel_requests(num_requests, max_workers=3)
    test_parallel_requests(num_requests, max_workers=5)
    
    # 异步测试
    try:
        asyncio.run(test_async_requests(num_requests))
    except ImportError:
        print("\n=== aiohttp未安装，跳过异步测试 ===")
    
    print("\n测试完成！")
