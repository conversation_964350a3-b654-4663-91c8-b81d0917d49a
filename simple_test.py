#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import requests
import httpx
import os
import sys

# 禁用代理
os.environ.pop('HTTP_PROXY', None)
os.environ.pop('HTTPS_PROXY', None)
os.environ.pop('http_proxy', None)
os.environ.pop('https_proxy', None)

# 配置
PROXY_CONFIG = {
    "base_url": "http://14.103.165.110:12000/v1/chat/completions",
    "api_key": "sk-zR4LZYvBUvYj6jYf4TvaYAeXBRxtr7U0EJFzB87NQNw81DAj",
    "model": "doubao-seed-1-6-250615"
}

def create_test_request():
    """创建测试请求"""
    return {
        "model": "doubao-seed-1-6-250615",
        "messages": [{"role": "user", "content": "Hello! Please respond briefly."}],
        "stream": False,
        "max_tokens": 50
    }

def test_requests_session():
    """测试requests库使用session"""
    print("=== 测试requests库(使用session) ===")
    sys.stdout.flush()
    
    num_requests = 5
    start_time = time.time()
    
    with requests.Session() as session:
        session.proxies = {'http': None, 'https': None}
        session.headers.update({
            "Content-Type": "application/json",
            "Authorization": f"Bearer {PROXY_CONFIG['api_key']}"
        })
        
        for i in range(num_requests):
            try:
                print(f"发送请求 {i+1}...")
                sys.stdout.flush()
                response = session.post(
                    PROXY_CONFIG["base_url"],
                    json=create_test_request(),
                    timeout=30
                )
                print(f"请求{i+1}: {response.status_code}")
                sys.stdout.flush()
            except Exception as e:
                print(f"请求{i+1}失败: {e}")
                sys.stdout.flush()
    
    end_time = time.time()
    total_time = end_time - start_time
    print(f"总耗时: {total_time:.2f}秒")
    print(f"平均每请求: {total_time/num_requests:.2f}秒")
    sys.stdout.flush()
    return total_time

def test_httpx_client():
    """测试httpx客户端"""
    print("\n=== 测试httpx客户端 ===")
    sys.stdout.flush()
    
    num_requests = 5
    start_time = time.time()
    
    with httpx.Client(
        timeout=httpx.Timeout(connect=5, read=120, write=30, pool=10),
        limits=httpx.Limits(max_connections=100, max_keepalive_connections=20)
    ) as client:
        for i in range(num_requests):
            try:
                print(f"发送请求 {i+1}...")
                sys.stdout.flush()
                response = client.post(
                    PROXY_CONFIG["base_url"],
                    headers={
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {PROXY_CONFIG['api_key']}"
                    },
                    json=create_test_request()
                )
                print(f"请求{i+1}: {response.status_code}")
                sys.stdout.flush()
            except Exception as e:
                print(f"请求{i+1}失败: {e}")
                sys.stdout.flush()
    
    end_time = time.time()
    total_time = end_time - start_time
    print(f"总耗时: {total_time:.2f}秒")
    print(f"平均每请求: {total_time/num_requests:.2f}秒")
    sys.stdout.flush()
    return total_time

if __name__ == "__main__":
    print("开始HTTP性能测试...")
    sys.stdout.flush()
    
    time1 = test_requests_session()
    time2 = test_httpx_client()
    
    print(f"\n=== 总结 ===")
    print(f"requests+session: {time1:.2f}秒")
    print(f"httpx客户端: {time2:.2f}秒")
    if time1 > 0:
        print(f"httpx比requests快: {((time1-time2)/time1*100):.1f}%")
    sys.stdout.flush()
