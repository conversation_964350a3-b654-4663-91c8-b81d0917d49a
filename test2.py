import os
import json
import datetime
import re
import markdown
from bs4 import BeautifulSoup
from pypinyin import pinyin, Style
import multiprocessing # Import multiprocessing module
from concurrent.futures import ThreadPoolExecutor  # 新增：用于线程池并发控制
import sys # sys is needed for sys.path.append and sys.exit
import time  # 新增：用于记录时间
from tqdm import tqdm  # 新增：导入进度条模块
import requests  # 新增：用于HTTP请求
import httpx  # 新增：用于高性能HTTP请求和连接池

# Ensure volcengine-python-sdk-master is in sys.path
sys.path.append(os.path.join(os.path.dirname(__file__), 'volcengine-python-sdk-master'))
from image_utils import image_to_base64, validate_base64
from yolo_utils import parse_yolo_annotation
from ai_utils import extract_json_from_response, read_system_prompt, ask_user_for_system_prompt, build_messages_with_system_prompt

# 新增：中模型代理配置
PROXY_CONFIG = {
    "base_url": "http://**************:12000/v1/chat/completions",
    "api_key": "sk-zR4LZYvBUvYj6jYf4TvaYAeXBRxtr7U0EJFzB87NQNw81DAj",
    "model": "doubao-seed-1-6-250615"
}

# 新增：全局HTTP客户端连接池（优化性能）
HTTP_CLIENT = httpx.Client(
    timeout=httpx.Timeout(connect=5, read=120, write=30, pool=10),  # 连接5秒，读取120秒，写入30秒，连接池10秒
    limits=httpx.Limits(max_connections=100, max_keepalive_connections=20)  # 连接池配置
)

# 常见HTTP状态码说明
HTTP_STATUS_EXPLANATIONS = {
    400: "Bad Request - 请求格式错误",
    401: "Unauthorized - 认证失败，请检查API Key",
    403: "Forbidden - 访问被拒绝",
    404: "Not Found - 请求的资源不存在",
    429: "Too Many Requests - 请求频率过高，请稍后重试",
    500: "Internal Server Error - 服务器内部错误",
    502: "Bad Gateway - 网关错误，代理服务器无法连接到上游服务",
    503: "Service Unavailable - 服务暂时不可用",
    504: "Gateway Timeout - 网关超时"
}

def call_proxy_api(messages, model_id=None, response_format=None, temperature=None, top_p=None, max_tokens=None):
    """
    调用中模型代理API（优化版：使用连接池和合理超时）
    """
    # 重试配置
    max_retries = 2
    retry_delay = 2  # 秒

    for attempt in range(max_retries):
        try:
            # 使用传入的模型ID，如果没有则使用默认配置
            model = model_id if model_id else PROXY_CONFIG["model"]

            # 构建请求数据
            request_data = {
                "model": model,
                "messages": messages,
                "stream": False
            }

            # 添加可选参数
            if temperature is not None:
                request_data["temperature"] = temperature
            if top_p is not None:
                request_data["top_p"] = top_p
            if max_tokens is not None:
                request_data["max_tokens"] = max_tokens
            if response_format == "json_object":
                request_data["response_format"] = {"type": "json_object"}

            # 发送HTTP请求（使用连接池客户端）
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {PROXY_CONFIG['api_key']}"
            }

            response = HTTP_CLIENT.post(
                PROXY_CONFIG["base_url"],
                headers=headers,
                json=request_data
            )
            
            if response.status_code == 200:
                result = response.json()
                if attempt > 0:
                    print(f"重试成功！第 {attempt + 1} 次尝试")
                return result
            elif response.status_code in [502, 503, 504]:
                # 这些错误通常是临时性的，需要重试
                status_explanation = HTTP_STATUS_EXPLANATIONS.get(response.status_code, "未知错误")
                if attempt < max_retries - 1:
                    print(f"遇到{response.status_code}错误（{status_explanation}），第 {attempt + 1} 次尝试失败，等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                    continue
                else:
                    raise Exception(f"API请求失败，状态码: {response.status_code} ({status_explanation}), 响应: {response.text}")
            else:
                status_explanation = HTTP_STATUS_EXPLANATIONS.get(response.status_code, "未知错误")
                raise Exception(f"API请求失败，状态码: {response.status_code} ({status_explanation}), 响应: {response.text}")
                
        except (httpx.RequestError, httpx.HTTPStatusError, httpx.TimeoutException) as e:
            if attempt < max_retries - 1:
                print(f"网络请求异常，第 {attempt + 1} 次尝试失败，等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                retry_delay *= 2  # 指数退避
                continue
            else:
                raise Exception(f"网络请求失败: {str(e)}")
        except Exception as e:
            if attempt < max_retries - 1:
                print(f"其他异常，第 {attempt + 1} 次尝试失败，等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                retry_delay *= 2  # 指数退避
                continue
            else:
                raise Exception(f"API调用失败: {str(e)}")
    
    raise Exception("所有重试都失败了")

def generate_default_error_json(question_count=1, json_type="answer"):
    """
    生成默认的错误JSON格式
    question_count: 题目数量
    json_type: "answer" 表示答案格式，"boolean" 表示布尔格式
    """
    if json_type == "boolean":
        # 对于test2.py, test3.py, one_stage_test.py，返回false的布尔值
        return json.dumps({f"题目{i}": False for i in range(1, question_count + 1)}, ensure_ascii=False)
    else:
        # 对于test.py，返回"API请求失败"的答案格式
        return json.dumps({f"题目 {i}": "API请求失败" for i in range(1, question_count + 1)}, ensure_ascii=False)

def extract_question_count_from_json(json_str):
    """
    从JSON字符串中提取题目数量
    """
    if not json_str:
        return 1
    try:
        data = json.loads(json_str)
        if isinstance(data, dict):
            return len(data)
    except:
        pass
    return 1

# --- Auxiliary Functions (Unchanged from your provided code) ---

def markdown_to_text(markdown_content):
    """将markdown格式转换为纯文本"""
    # 将markdown转换为HTML
    html = markdown.markdown(markdown_content)
    # 使用BeautifulSoup提取纯文本
    soup = BeautifulSoup(html, 'html.parser')
    # 获取纯文本内容
    text = soup.get_text()
    # 清理多余的空白字符
    text = re.sub(r'\n\s*\n', '\n\n', text)  # 将多个空行替换为两个换行
    text = re.sub(r'[ \t]+', ' ', text)  # 将多个空格替换为单个空格
    return text.strip()

def chinese_to_pinyin(chinese_text):
    """将中文转换为拼音（无声调）"""
    pinyin_list = pinyin(chinese_text, style=Style.NORMAL)
    return ''.join([p[0] for p in pinyin_list])

def get_model_choice():
    """让用户选择模型ID"""
    available_models = {
        "1": "doubao-seed-1-6-250615",
        "2": "doubao-seed-1-6-flash-250715",
        "3": "doubao-1-5-thinking-vision-pro-250428",
        "4": "doubao-1-5-vision-pro-32k-250115"
    }

    print("请选择模型ID：")
    for key, value in available_models.items():
        print(f"{key}. {value}")

    while True:
        user_input = input("请输入模型编号（1-4）：").strip()
        if user_input in available_models:
            selected_model = available_models[user_input]
            print(f"选择的模型：{selected_model}")
            return selected_model
        else:
            print("输入无效，请输入 1-4 的数字")


def get_response_format_choice(model_id):
    """根据模型ID判断是否支持jsonObject，如果支持则让用户选择response_format"""
    # 支持jsonObject的模型列表（模型1、2、3）
    json_object_supported_models = [
        "doubao-seed-1-6-250615",
        "doubao-seed-1-6-flash-250715",
        "doubao-1-5-thinking-vision-pro-250428"
    ]

    if model_id in json_object_supported_models:
        print("选择response_format：")
        print("1. text")
        print("2. json_object")

        while True:
            user_input = input("请输入选择（1-2）：").strip()
            if user_input == "1":
                return "text"
            elif user_input == "2":
                return "json_object"
            else:
                print("输入无效，请输入 1 或 2")
    else:
        return "text"  # 不支持jsonObject的模型默认使用text


def get_max_tokens_for_model(model_id):
    """根据模型ID返回对应的max_tokens值"""
    if model_id == "doubao-1-5-vision-pro-32k-250115":  # 模型4
        return 12288  # 12K
    else:  # 模型1、2、3
        return 16384  # 16K

def get_grading_mode():
    """让用户选择批改模式"""
    print("请选择批改模式：")
    print("1. 使用大模型批改")
    print("2. 使用JSON比对")

    while True:
        user_input = input("请输入模式编号（1-2）：").strip()
        if user_input == "1":
            print("选择的模式：大模型批改")
            return "model"
        elif user_input == "2":
            print("选择的模式：JSON比对")
            return "json_compare"
        else:
            print("输入无效，请输入 1 或 2")

def get_question_type():
    """获取用户输入的题型并转换为拼音路径"""
    question_types = {
        "1": "涂卡选择题", "2": "涂卡判断题", "3": "连线题",
        "4": "图表题", "5": "翻译题", "6": "画图题",
        "7": "数学应用题", "8": "数学计算题", "9": "简单的四则运算",
        "10": "填空题", "11": "判断题", "12": "多选题", "13": "单选题"
    }

    print("请选择题型：")
    for key, value in question_types.items():
        print(f"{key}. {value}")

    while True:
        user_input = input("请输入题型编号（1-13）或直接输入中文：").strip()

        # 检查数字输入
        if user_input in question_types:
            question_type = question_types[user_input]
            break
        # 检查中文输入
        elif user_input in question_types.values():
            question_type = user_input
            break
        else:
            print("输入无效，请输入 1-13 的数字或直接输入中文题型名称")

    # 转换为拼音
    pinyin_name = chinese_to_pinyin(question_type)
    print(f"选择的题型：{question_type}")
    print(f"对应的拼音路径：{pinyin_name}")

    return question_type, pinyin_name

def extract_json_responses(md_path):
    """提取md文件中所有响应内容的JSON"""
    results = []
    with open(md_path, "r", encoding="utf-8") as f:
        content = f.read()

    # 匹配所有 ### 响应内容： 后面紧跟的代码块内容
    resp_pattern = r"### 响应内容：\s*```json\s*([\s\S]*?)\s*```"
    resp_matches = re.findall(resp_pattern, content)

    for i, match in enumerate(resp_matches, 1):
        json_str = match.strip()
        try:
            # 验证JSON格式
            json.loads(json_str) # Just load to validate, don't store object
            results.append(json_str)
        except json.JSONDecodeError as e: # More specific error handling
            print(f"警告：第 {i} 个响应不是有效JSON格式 '{json_str[:50]}...' (错误: {str(e)})")
            print(f"将其转换为标准JSON格式进行处理")
            # 将无效JSON转换为标准JSON格式
            converted_json = convert_to_standard_json(json_str, i)
            results.append(converted_json)
        except Exception as e:
            print(f"警告：第 {i} 个响应处理失败，未知错误: {str(e)}")
            # 将异常内容也转换为标准JSON格式
            converted_json = convert_to_standard_json(json_str, i)
            results.append(converted_json)

    return results

def convert_to_standard_json(invalid_content, index):
    """
    将无效的JSON内容转换为标准JSON格式
    """
    # 创建一个标准的JSON格式，表示无法识别的内容
    standard_json = {
        f"题目{index}": "无法识别"
    }
    return json.dumps(standard_json, ensure_ascii=False)

def get_latest_md_file(response_dir):
    """获取response文件夹中时间最晚的md文件（排除answer.md和response_template.md）"""
    md_files = []
    if not os.path.exists(response_dir):
        return None
    for filename in os.listdir(response_dir):
        if filename.endswith('.md') and filename not in ['answer.md', 'response_template.md']:
            md_files.append(filename)
    
    if not md_files:
        return None
    
    # Sort by filename (which includes timestamp), return the latest
    md_files.sort()
    return os.path.join(response_dir, md_files[-1])

# --- JSON比对处理函数 ---
def process_single_json_pair_compare(test_json, answer_json, index):
    """
    使用JSON比对方式处理单对JSON响应
    """
    sep = f"\n{'='*50}\n"
    info = f"处理第 {index} 组JSON响应"

    # 移除控制台输出，改为使用进度条显示
    # print(f"正在比对：第 {index} 组JSON响应")

    current_output_lines = []
    current_output_lines.append(sep)
    current_output_lines.append(info + "\n")
    current_output_lines.append(sep)

    try:
        # 记录开始时间
        start_time = time.time()

        # 解析学生答案和正确答案的JSON
        try:
            test_json_safe = test_json.strip() if test_json is not None else ""
            answer_json_safe = answer_json.strip() if answer_json is not None else ""
            student_answers = json.loads(test_json_safe)
            correct_answers = json.loads(answer_json_safe)
        except json.JSONDecodeError as e:
            raise Exception(f"JSON解析失败: {str(e)}")

        # 逐题比对，生成true/false结果
        compare_result = {}

        # 按照题号数字顺序排序正确答案和学生答案
        def extract_question_number(key):
            import re
            match = re.search(r'\d+', str(key))
            if match:
                return int(match.group())
            return 0

        # 对正确答案按题号排序
        sorted_correct_items = sorted(correct_answers.items(), key=lambda x: extract_question_number(x[0]))
        sorted_student_items = sorted(student_answers.items(), key=lambda x: extract_question_number(x[0]))

        # 提取排序后的值列表
        correct_values = [item[1] for item in sorted_correct_items]
        student_values = [item[1] for item in sorted_student_items]

        # 按照正确答案的key顺序进行比对
        for i, (correct_key, correct_value) in enumerate(sorted_correct_items):
            if i < len(student_values):
                # 学生答案中有对应位置的值，进行比对
                compare_result[correct_key] = student_values[i] == correct_value
            else:
                # 学生答案中没有对应位置的值，标记为false
                compare_result[correct_key] = False

        # 记录结束时间并计算响应时间
        end_time = time.time()
        response_time = end_time - start_time

        # compare_result已经按照正确答案的key顺序构建，无需再次排序
        sorted_compare_result = compare_result

        # 格式化比对结果为JSON字符串
        resp_content = json.dumps(sorted_compare_result, ensure_ascii=False, separators=(',', ':'))

        # Append results to output lines for this specific task
        current_output_lines.append(f"### 学生答案：\n")
        current_output_lines.append("```json\n")
        current_output_lines.append(f"{test_json}\n")
        current_output_lines.append("```\n\n")

        current_output_lines.append(f"### 正确答案：\n")
        current_output_lines.append("```json\n")
        current_output_lines.append(f"{answer_json}\n")
        current_output_lines.append("```\n\n")

        current_output_lines.append(f"### 比对结果：\n")
        current_output_lines.append("```json\n")
        current_output_lines.append(f"{resp_content}\n")
        current_output_lines.append("```\n")
        # 添加响应时间记录
        current_output_lines.append(f"### 响应时间：{response_time:.4f}秒\n")

        return {
            'success': True,
            'index': index,
            'output_lines': current_output_lines,
            'model_response_json': resp_content
        }
    except Exception as e:
        err_msg = f"处理第 {index} 组JSON响应时出错: {str(e)}"
        # 只在失败时打印错误信息
        print(f"比对错误: {err_msg}")
        current_output_lines.append(err_msg + "\n")
        return {
            'success': False,
            'index': index,
            'output_lines': current_output_lines,
            'model_response_json': None
        }

# --- 关键修改：并行处理函数 `process_single_json_pair_parallel` 的定义 ---
# 此函数定义在 `if __name__ == "__main__":` 之外，以兼容多进程
# 参数列表已修改，直接接收 5 个参数
def process_single_json_pair_parallel(test_json, answer_json, round2_prompt_without_images_text, index, client_api_key, model_id, response_format="text", temperature=1, top_p=0.7, max_tokens=None, system_prompt=None):
    """
    Processes a single pair of JSON responses with the model in parallel.
    """

    sep = f"\n{'='*50}\n"
    info = f"处理第 {index} 组JSON响应"

    # 移除控制台输出，改为使用进度条显示
    # print(f"进程 {os.getpid()} 正在处理：第 {index} 组JSON响应")

    current_output_lines = []
    current_output_lines.append(sep)
    current_output_lines.append(info + "\n")
    current_output_lines.append(sep)

    # 重试机制：最多重试2次，只对Connection error进行重试
    max_retries = 2
    for attempt in range(max_retries + 1):
        try:
            # Construct request content
            content = [
                {"type": "text", "text": test_json},
                {"type": "text", "text": answer_json},
                {"type": "text", "text": round2_prompt_without_images_text}
            ]

            # 记录开始时间
            start_time = time.time()

            # 构建未加密的显示版本内容（与实际content格式保持一致）
            content_display = [
                {"type": "text", "text": test_json},
                {"type": "text", "text": answer_json},
                {"type": "text", "text": round2_prompt_without_images_text}
            ]

            # 构建请求参数（先保存未加密版本用于显示）
            # 使用新的messages构建函数，支持system prompt
            user_content = f"{test_json}\n\n{answer_json}\n\n{round2_prompt_without_images_text}"
            messages = build_messages_with_system_prompt(
                user_content=user_content,
                system_prompt=system_prompt,
                include_image=False
            )
            
            request_params_display = {
                "model": model_id,
                "messages": messages,
                "max_tokens": max_tokens if max_tokens is not None else get_max_tokens_for_model(model_id),
                "thinking": {"type": "disabled"}
            }

            # 只有当temperature不为None时才添加到请求体中
            if temperature is not None:
                request_params_display["temperature"] = temperature

            # 只有当top_p不为None时才添加到请求体中
            if top_p is not None:
                request_params_display["top_p"] = top_p

            # 构建实际请求参数（包含加密头）
            request_params = {
                "model": model_id,
                "messages": messages,  # 使用上面构建的messages
                "extra_headers": {'x-is-encrypted': 'true'},
                "max_tokens": max_tokens if max_tokens is not None else get_max_tokens_for_model(model_id),
                "thinking": {"type": "disabled"}
            }

            # 只有当temperature不为None时才添加到请求体中
            if temperature is not None:
                request_params["temperature"] = temperature

            # 只有当top_p不为None时才添加到请求体中
            if top_p is not None:
                request_params["top_p"] = top_p

            # 如果选择了json_object格式，添加response_format参数
            if response_format == "json_object":
                request_params["response_format"] = {"type": "json_object"}
                request_params_display["response_format"] = {"type": "json_object"}

            # 调用代理API
            response = call_proxy_api(
                messages=request_params["messages"],
                model_id=model_id,
                response_format=response_format,
                temperature=temperature,
                top_p=top_p,
                max_tokens=max_tokens if max_tokens is not None else get_max_tokens_for_model(model_id)
            )
            # 记录结束时间并计算响应时间
            end_time = time.time()
            response_time = end_time - start_time

            # 处理代理API的响应格式
            raw_content = response['choices'][0]['message']['content']
            if raw_content is None:
                raise Exception("API返回的响应内容为空")

            resp_content = raw_content.strip()
            if resp_content.startswith("```json"):
                resp_content = resp_content[7:]
            if resp_content.startswith("```"):
                resp_content = resp_content[3:]
            if resp_content.endswith("```"):
                resp_content = resp_content[:-3]
            resp_content = resp_content.strip()

            # 如果是重试成功，添加重试信息
            if attempt > 0:
                current_output_lines.append(f"### 重试信息：第 {attempt + 1} 次尝试成功\n")

            # Append results to output lines for this specific task
            current_output_lines.append(f"### 学生答案：\n")
            current_output_lines.append("```json\n")
            current_output_lines.append(f"{test_json}\n")
            current_output_lines.append("```\n\n")

            current_output_lines.append(f"### 正确答案：\n")
            current_output_lines.append("```json\n")
            current_output_lines.append(f"{answer_json}\n")
            current_output_lines.append("```\n\n")

            current_output_lines.append(f"### 模型回答：\n")
            current_output_lines.append("```json\n")
            current_output_lines.append(f"{resp_content}\n")
            current_output_lines.append("```\n")

            # 添加请求体（使用未加密版本，test2.py中没有图片）
            current_output_lines.append(f"### 请求体：\n")
            current_output_lines.append("```json\n")
            current_output_lines.append(f"{json.dumps(request_params_display, ensure_ascii=False, indent=2)}\n")
            current_output_lines.append("```\n")

            # 添加响应时间记录
            current_output_lines.append(f"### 响应时间：{response_time:.2f}秒\n")

            return {
                'success': True,
                'index': index,
                'output_lines': current_output_lines,
                'model_response_json': resp_content # To be used for comparison later if needed
            }
        except Exception as e:
            error_str = str(e)
            # 检查是否为Connection error，如果是且还有重试机会，则继续重试
            if "Connection error" in error_str and attempt < max_retries:
                print(f"进程 {os.getpid()} 处理第 {index} 组JSON响应时出现连接错误，正在重试... (第 {attempt + 1} 次尝试)")
                time.sleep(1)  # 等待1秒后重试
                continue

            # 如果不是Connection error或已达到最大重试次数，则返回失败
            err_msg = f"处理第 {index} 组JSON响应时出错: {error_str}"
            if attempt > 0:
                err_msg += f" (已重试 {attempt} 次)"
            # 只在失败时打印错误信息
            print(f"进程 {os.getpid()} 错误: {err_msg}")
            current_output_lines.append(err_msg + "\n")

            # 生成简单的错误JSON格式
            error_json = json.dumps({"请求异常": error_str}, ensure_ascii=False)

            current_output_lines.append(f"### 学生答案：\n")
            current_output_lines.append("```json\n")
            current_output_lines.append(f"{test_json}\n")
            current_output_lines.append("```\n\n")

            current_output_lines.append(f"### 正确答案：\n")
            current_output_lines.append("```json\n")
            current_output_lines.append(f"{answer_json}\n")
            current_output_lines.append("```\n\n")

            current_output_lines.append(f"### 模型回答：\n")
            current_output_lines.append("```json\n")
            current_output_lines.append(f"{error_json}\n")
            current_output_lines.append("```\n")

            return {
                'success': False,
                'index': index,
                'output_lines': current_output_lines,
                'model_response_json': error_json
            }

# --- Compare Wrong Questions Functions (Unchanged from your provided code) ---

def extract_json_responses_and_names(md_path):
    """提取md文件中所有响应名和响应内容（返回列表: [(response_name, json_str)]）"""
    results = []
    with open(md_path, "r", encoding="utf-8") as f:
        content = f.read()
    # 匹配所有"处理第 n 组JSON响应"
    resp_pattern = r"处理第 (\d+) 组JSON响应"
    resp_matches = re.findall(resp_pattern, content)
    # 匹配所有 ### 模型回答： 或 ### 比对结果： 后面紧跟的代码块内容
    model_pattern = r"### (?:模型回答|比对结果)：\s*```json\s*([\s\S]*?)\s*```"
    model_matches = re.findall(model_pattern, content)
    # 标准化json
    norm_jsons = []
    for i, m in enumerate(model_matches, 1):
        json_str = m.strip()
        try:
            obj = json.loads(json_str)
            # 按照题号数字顺序排序，而不是字符串顺序
            def extract_question_number(key):
                """从题号中提取数字部分"""
                import re
                # 匹配数字部分
                match = re.search(r'\d+', str(key))
                if match:
                    return int(match.group())
                return 0  # 如果没有数字，返回0

            # 按照题号数字排序
            sorted_items = sorted(obj.items(), key=lambda x: extract_question_number(x[0]))
            sorted_obj = dict(sorted_items)
            norm = json.dumps(sorted_obj, ensure_ascii=False, separators=(',', ':'))
            norm_jsons.append(norm)
        except Exception as e:
            print(f"第 {i} 个模型回答不是有效JSON格式: {str(e)}")
            print(f"将其转换为标准JSON格式进行处理")
            # 将无效JSON转换为标准JSON格式
            converted_json = convert_to_standard_json(json_str, i)
            norm_jsons.append(converted_json)
    # 按顺序配对
    for i in range(max(len(resp_matches), len(norm_jsons))):
        resp_name = f"第{resp_matches[i]}组" if i < len(resp_matches) else None
        norm_json = norm_jsons[i] if i < len(norm_jsons) else None
        results.append((resp_name, norm_json))
    return results

# --- Main Program Execution ---

# Wrap the main program execution in a `if __name__ == "__main__":` block.
# This is crucial for multiprocessing, especially on Windows.
if __name__ == "__main__":
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='运行test2.py脚本')
    parser.add_argument('--config', help='配置文件路径')
    args = parser.parse_args()

    # Your API Key (ensure it's handled securely, e.g., from environment variables)
    api_key_from_client_init = "36c2aa0e-8b2b-4412-bc92-d3d1cef96b1b"

    if args.config and os.path.exists(args.config):
        # 从配置文件加载参数
        print("从配置文件加载参数...")
        with open(args.config, 'r', encoding='utf-8') as f:
            config = json.load(f)

        grading_mode = config.get('grading_mode', 'model')  # 默认使用模型批改
        selected_model = config['model_id']
        response_format = config.get('response_format', 'text')  # 默认使用text格式
        question_type = config['question_type']
        pinyin_name = config['pinyin_name']
        custom_prompt = config.get('test2_prompt')  # 获取自定义prompt
        # 获取API参数，如果没有配置则使用默认值
        temperature = config.get('temperature', 1)
        top_p = config.get('top_p', 0.7)
        max_tokens = config.get('max_tokens')

        print(f"使用配置：批改模式={grading_mode}, 模型={selected_model}, response_format={response_format}, 题型={question_type}")
        if custom_prompt:
            print("将使用从main脚本传递的自定义提示词")
    else:
        # 交互模式获取参数
        print("配置文件不存在，使用交互模式...")

        # Get user-selected grading mode
        grading_mode = get_grading_mode()

        # Get user-selected model (only if using model grading)
        if grading_mode == "model":
            selected_model = get_model_choice()
            response_format = get_response_format_choice(selected_model)
        else:
            selected_model = "JSON比对模式"
            response_format = "text"

        # Get user-selected question type (Sequential)
        question_type, pinyin_name = get_question_type()
        custom_prompt = None  # 交互模式下没有自定义prompt
        # 交互模式下使用默认的API参数
        temperature = 1
        top_p = 0.7
        max_tokens = None

    # Build type-related paths (Sequential)
    types_dir = "types"
    question_dir = os.path.join(types_dir, pinyin_name)
    response_dir = os.path.join(question_dir, "response")
    round2_response_without_images_dir = os.path.join(question_dir, "round2_response_without_images")
    round2_prompt_without_images_file = os.path.join(question_dir, "round2_prompt_without_images.md")
    answer_file = os.path.join(response_dir, "answer.md")

    # Create round2_response_without_images folder (Sequential)
    os.makedirs(round2_response_without_images_dir, exist_ok=True)

    # Check for necessary files (Sequential)
    if not custom_prompt and not os.path.exists(round2_prompt_without_images_file):
        print(f"错误：round2_prompt_without_images文件 {round2_prompt_without_images_file} 不存在！")
        print(f"请创建 {round2_prompt_without_images_file} 文件并写入提示词内容")
        sys.exit()

    if not os.path.exists(answer_file):
        print(f"错误：answer文件 {answer_file} 不存在！")
        sys.exit()

    # Get the latest md file from response_dir (Sequential)
    latest_md_file = get_latest_md_file(response_dir)
    if not latest_md_file:
        print(f"错误：在 {response_dir} 中没有找到md文件！")
        sys.exit()

    print(f"找到时间最晚的md文件：{latest_md_file}")

    # 优先使用自定义prompt，否则从round2_prompt_without_images.md文件读取提示词
    if custom_prompt:
        round2_prompt_without_images_text = custom_prompt
        print("使用从main脚本传递的自定义提示词")
    else:
        try:
            with open(round2_prompt_without_images_file, 'r', encoding='utf-8') as f:
                round2_markdown_prompt = f.read().strip()
            print(f"已从文件 {round2_prompt_without_images_file} 读取round2_prompt_without_images")
            round2_prompt_without_images_text = markdown_to_text(round2_markdown_prompt)
            print("已将markdown格式转换为纯文本")
        except Exception as e:
            print(f"读取round2_prompt_without_images文件时出错：{str(e)}")
            sys.exit()

        if not round2_prompt_without_images_text:
            print("错误：round2_prompt_without_images文件为空！")
            sys.exit()

    print(f"使用的提示词: {round2_prompt_without_images_text}")

    # Extract JSON responses (Sequential)
    print("正在提取时间最晚的md文档中的JSON响应...")
    test_json_responses = extract_json_responses(latest_md_file)
    print(f"从时间最晚的md文档中提取到 {len(test_json_responses)} 个JSON响应")

    print("正在提取answer.md文档中的JSON响应...")
    answer_json_responses = extract_json_responses(answer_file)
    print(f"从answer.md文档中提取到 {len(answer_json_responses)} 个JSON响应")

    # Check JSON response count consistency (Sequential)
    if len(test_json_responses) != len(answer_json_responses):
        print(f"警告：时间最晚的md文档和answer.md文档中的JSON响应数量不一致！")
        print(f"时间最晚的md文档: {len(test_json_responses)}, answer: {len(answer_json_responses)}")
        min_count = min(len(test_json_responses), len(answer_json_responses))
        test_json_responses = test_json_responses[:min_count]
        answer_json_responses = answer_json_responses[:min_count]
        print(f"将使用前 {min_count} 个响应进行处理")

    # Generate round2_response_without_images file path (Sequential)
    now = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    round2_response_without_images_file = os.path.join(round2_response_without_images_dir, f"{now}.md")
    output_lines = []

    # Write header (Sequential) - 预留准确率位置
    output_lines.append(f"# 运行时间: {now}\n\n")
    if grading_mode == "model":
        output_lines.append(f"**批改方式：** 模型交互\n\n")
        output_lines.append(f"**使用模型ID：** {selected_model}\n\n")
        output_lines.append(f"## 使用的prompt\n\n")
        output_lines.append(f"{round2_prompt_without_images_text}\n\n")
    else:
        output_lines.append(f"**批改方式：** JSON比对\n\n")
        output_lines.append(f"**比对说明：** 直接比对学生答案和正确答案的JSON字符串\n\n")

    # --- 开始处理JSON响应对 ---
    if grading_mode == "model":
        print("\n--- 开始并行处理JSON响应对并与模型交互 ---\n")

        # Prepare tasks for parallel processing
        tasks = []
        for i, (test_json, answer_json) in enumerate(zip(test_json_responses, answer_json_responses), 1):
            # 确保这里传入的参数顺序和数量与 process_single_json_pair_parallel 的定义一致
            tasks.append((test_json, answer_json, round2_prompt_without_images_text, i, api_key_from_client_init, selected_model, response_format, temperature, top_p, max_tokens))

        # Set the number of processes
        # 优化：使用线程池而不是进程池，并限制并发数量
        max_workers = min(8, os.cpu_count() if os.cpu_count() else 4)  # 限制最大8个并发
        print(f"将使用 {max_workers} 个线程进行并行处理（优化版）。")

        # 使用进度条显示API推理进度
        all_processed_results = []
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 使用map配合手动进度条更新
            with tqdm(total=len(tasks), desc="API推理", unit="组") as pbar:
                # 分批处理以便更新进度条，减小批次大小以降低API压力
                batch_size = max(1, min(5, len(tasks) // max_workers))  # 更小的批次大小
                for i in range(0, len(tasks), batch_size):
                    batch_tasks = tasks[i:i + batch_size]
                    # 使用starmap等效的方式处理参数
                    batch_results = list(executor.map(lambda args: process_single_json_pair_parallel(*args), batch_tasks))
                    all_processed_results.extend(batch_results)
                    pbar.update(len(batch_tasks))
                    # 添加小延迟以减轻API压力
                    time.sleep(0.1)

        print("\n--- 并行处理完成，合并结果 ---\n")
    else:
        print("\n--- 开始JSON比对处理 ---\n")

        all_processed_results = []
        # 使用进度条显示JSON比对进度
        for i, (test_json, answer_json) in enumerate(tqdm(zip(test_json_responses, answer_json_responses),
                                                          total=len(test_json_responses),
                                                          desc="JSON比对", unit="组"), 1):
            result = process_single_json_pair_compare(test_json, answer_json, i)
            all_processed_results.append(result)

        print("\n--- JSON比对处理完成 ---\n")

    # Collect results in order
    for result in all_processed_results:
        output_lines.extend(result['output_lines'])

    output_lines.append(f"\n{'='*50}\n")
    output_lines.append("所有JSON响应处理完成！\n")
    output_lines.append(f"{'='*50}\n")
    print(f"\n{'='*50}\n")
    print("所有JSON响应处理完成！")
    print(f"{'='*50}\n")
    # --- 并行处理结束 ---

    # Write new content to the response file (Sequential)
    with open(round2_response_without_images_file, "w", encoding="utf-8") as f:
        f.writelines(output_lines)

    # If response_template.md doesn't exist, copy the current md file as a template (Sequential)
    template_path = os.path.join(round2_response_without_images_dir, "response_template.md")
    if not os.path.exists(template_path):
        import shutil
        shutil.copyfile(round2_response_without_images_file, template_path)
        print(f"已创建response_template.md模板文件")

    # Compare wrong questions functionality (Sequential)
    template_path = os.path.join(round2_response_without_images_dir, "response_template.md")
    wrong_items = []
    accuracy_str = ""
    if os.path.exists(template_path):
        new_results = extract_json_responses_and_names(round2_response_without_images_file)
        template_results = extract_json_responses_and_names(template_path)
        wrongs = []
        error_msgs = []
        
        # Check response count consistency
        if len(new_results) != len(template_results):
            error_msgs.append(f"响应数量不一致：本次{len(new_results)}，模板{len(template_results)}")
        
        min_len = min(len(new_results), len(template_results))
        for i in range(min_len):
            new_name, new_json = new_results[i]
            tpl_name, tpl_json = template_results[i]
            if new_name != tpl_name:
                wrongs.append(f"第 {i+1} 组响应: 响应名不一致，本次为 {new_name}，模板为 {tpl_name}")
                wrong_items.append(i+1)
            elif new_json != tpl_json:
                # 在比较前，对两个JSON进行值序列比较
                def compare_json_values(json_str1, json_str2):
                    """比较两个JSON的值序列，忽略键名差异"""
                    try:
                        obj1 = json.loads(json_str1)
                        obj2 = json.loads(json_str2)

                        # 按照题号数字顺序排序并提取值
                        def extract_question_number(key):
                            import re
                            match = re.search(r'\d+', str(key))
                            if match:
                                return int(match.group())
                            return 0

                        # 对两个JSON对象按题号排序并提取值序列
                        sorted_items1 = sorted(obj1.items(), key=lambda x: extract_question_number(x[0]))
                        sorted_items2 = sorted(obj2.items(), key=lambda x: extract_question_number(x[0]))

                        values1 = [item[1] for item in sorted_items1]
                        values2 = [item[1] for item in sorted_items2]

                        return values1 == values2
                    except:
                        # 如果JSON解析失败，回退到字符串比较，安全处理可能为None的字符串
                        str1 = json_str1.strip() if json_str1 is not None else ""
                        str2 = json_str2.strip() if json_str2 is not None else ""
                        return str1 == str2

                if not compare_json_values(new_json, tpl_json):
                    wrongs.append(f"第 {i+1} 组响应: {new_name}")
                    wrong_items.append(i+1)
                
        # Calculate accuracy
        total = min_len
        wrong_count = len(wrongs)
        accuracy = (total - wrong_count) / total if total > 0 else 1.0
        accuracy_str = f"准确率：{(accuracy*100):.2f}%  （({total} - {wrong_count}) / {total}）\n"

        # 保存准确率信息供返回使用
        accuracy_info = {
            'accuracy': accuracy,
            'accuracy_str': accuracy_str.strip(),
            'total': total,
            'wrong_count': wrong_count,
            'accuracy_percentage': f"{(accuracy*100):.2f}%"
        }
        
        # Construct new content for the top of the markdown file - 准确率置顶
        # 1. 准确率置顶
        new_top = f"## {accuracy_str}\n"

        # 2. 错题信息
        new_top += "## 错题\n"
        if error_msgs:
            for msg in error_msgs:
                new_top += f"- {msg}\n"
        if wrongs:
            for w in wrongs:
                new_top += f"- {w}\n"
        if not error_msgs and not wrongs:
            new_top += "本次无错题。\n"
        new_top += "\n"

        # Read old content and write new content
        with open(round2_response_without_images_file, "r", encoding="utf-8") as f:
            old_content = f.read()

        with open(round2_response_without_images_file, "w", encoding="utf-8") as f:
            f.write(new_top)
            f.write(old_content)
        # 控制台输出（只打印准确率，不打印详细错题列表）
        console_output = f"## {accuracy_str}\n"
        if not error_msgs and not wrongs:
            console_output += "## 错题\n本次无错题。\n"
        else:
            console_output += f"## 错题\n共 {len(wrongs) + len(error_msgs)} 项错题（详细信息已保存到文件）\n"
        print(console_output)

        # Create summary file for wrong items - 修复为包含完整4个JSON部分的格式
        if wrong_items:
            # Ensure round2_error_without_images directory exists
            round2_error_without_images_dir = os.path.join(question_dir, "round2_error_without_images")
            os.makedirs(round2_error_without_images_dir, exist_ok=True)
            summary_file = os.path.join(round2_error_without_images_dir, f"error_summary_{now}.md")

            # 提取学生答案和正确答案数据
            test_json_responses = extract_json_responses(latest_md_file)
            answer_json_responses = extract_json_responses(answer_file)

            summary_lines = []

            # 头部结构：准确率、运行时间置顶
            summary_lines.append(f"## 准确率：{(accuracy*100):.2f}%  （({total} - {wrong_count}) / {total}）\n\n")
            summary_lines.append(f"## 运行时间: {now}\n\n")

            # 添加模型ID信息（如果是模型交互模式）
            if grading_mode == "model":
                summary_lines.append(f"**使用模型ID：** {selected_model}\n\n")
                summary_lines.append(f"**批改方式：** 模型交互\n\n")
                summary_lines.append(f"## 使用的prompt\n\n{round2_prompt_without_images_text}\n\n")
            else:
                summary_lines.append(f"**批改方式：** JSON比对\n\n")
                summary_lines.append(f"**比对说明：** 直接比对学生答案和正确答案的JSON字符串\n\n")

            # 错题列表
            summary_lines.append(f"## 错题\n\n")
            for item in wrong_items:
                summary_lines.append(f"- 第 {item} 组响应\n")

            # 插入错题的详细内容，展示四个JSON（与test3.py保持一致）
            for idx in wrong_items:
                # 获取四个JSON
                student_json = test_json_responses[idx-1] if idx <= len(test_json_responses) else '{}'
                correct_json = answer_json_responses[idx-1] if idx <= len(answer_json_responses) else '{}'
                template_json = template_results[idx-1][1] if idx <= len(template_results) else '{}'
                response_json = new_results[idx-1][1] if idx <= len(new_results) else '{}'

                # 构建错题内容
                summary_lines.append(f"\n==================================================\n")
                summary_lines.append(f"处理第 {idx} 组JSON响应\n")
                summary_lines.append(f"==================================================\n")

                # 1. 学生答案
                summary_lines.append("### 学生答案：\n")
                summary_lines.append("```json\n")
                summary_lines.append(f"{student_json}\n")
                summary_lines.append("```\n\n")

                # 2. 正确答案
                summary_lines.append("### 正确答案：\n")
                summary_lines.append("```json\n")
                summary_lines.append(f"{correct_json}\n")
                summary_lines.append("```\n\n")

                # 3. response_template答案（模板答案）
                summary_lines.append("### response_template答案：\n")
                summary_lines.append("```json\n")
                summary_lines.append(f"{template_json}\n")
                summary_lines.append("```\n\n")

                # 4. 响应内容（本次生成的）
                summary_lines.append("### 响应内容：\n")
                summary_lines.append("```json\n")
                summary_lines.append(f"{response_json}\n")
                summary_lines.append("```\n")

            summary_lines.append("\n==================================================\n所有错题处理完成！\n==================================================\n")

            with open(summary_file, "w", encoding="utf-8") as f:
                f.writelines(summary_lines)

            print(f"已创建错题详细 summary.md 文件: {summary_file}")

    print(f"结果已保存到：{round2_response_without_images_file}")

    # 如果有准确率信息，打印准确率信息，否则打印默认值
    if 'accuracy_info' in locals():
        print(f"执行成功，准确率信息：{accuracy_info}")
    else:
        # 如果没有模板文件，打印默认准确率信息
        default_accuracy_info = {
            'accuracy': 0.0,
            'accuracy_str': '准确率：未知（无模板文件）',
            'total': 0,
            'wrong_count': 0,
            'accuracy_percentage': '未知'
        }
        print(f"执行成功，默认准确率信息：{default_accuracy_info}")

    print("test2.py 执行完成！")

def execute_test2_logic(grading_mode, selected_model, response_format, question_type, pinyin_name,
                       custom_prompt, temperature, top_p, max_tokens, api_key_from_client_init,
                       types_dir, question_dir, response_dir, round2_response_without_images_dir,
                       round2_prompt_without_images_file, answer_file, use_system_prompt=None):
    """
    执行test2的核心逻辑，返回准确率信息
    """
    try:
        # Get the latest md file from response_dir
        latest_md_file = get_latest_md_file(response_dir)
        if not latest_md_file:
            print(f"错误：在 {response_dir} 中没有找到md文件！")
            error_accuracy_info = {
                'accuracy': 0.0,
                'accuracy_str': '准确率：未找到响应文件',
                'total': 0,
                'wrong_count': 0,
                'accuracy_percentage': '错误'
            }
            return False, error_accuracy_info

        print(f"找到时间最晚的md文件：{latest_md_file}")

        # 优先使用自定义prompt，否则从round2_prompt_without_images.md文件读取提示词
        if custom_prompt:
            round2_prompt_without_images_text = custom_prompt
            print("使用从main脚本传递的自定义提示词")
        else:
            try:
                with open(round2_prompt_without_images_file, 'r', encoding='utf-8') as f:
                    round2_markdown_prompt = f.read().strip()
                print(f"已从文件 {round2_prompt_without_images_file} 读取round2_prompt_without_images")
                round2_prompt_without_images_text = markdown_to_text(round2_markdown_prompt)
                print("已将markdown格式转换为纯文本")
            except Exception as e:
                print(f"读取round2_prompt_without_images文件时出错：{str(e)}")
                error_accuracy_info = {
                    'accuracy': 0.0,
                    'accuracy_str': f'准确率：读取提示词失败 - {e}',
                    'total': 0,
                    'wrong_count': 0,
                    'accuracy_percentage': '错误'
                }
                return False, error_accuracy_info

            if not round2_prompt_without_images_text:
                print("错误：round2_prompt_without_images文件为空！")
                error_accuracy_info = {
                    'accuracy': 0.0,
                    'accuracy_str': '准确率：提示词文件为空',
                    'total': 0,
                    'wrong_count': 0,
                    'accuracy_percentage': '错误'
                }
                return False, error_accuracy_info

        print(f"使用的提示词: {round2_prompt_without_images_text}")

        # 处理system prompt参数
        if use_system_prompt is None:
            # 如果没有传入参数，则询问用户
            use_system_prompt = ask_user_for_system_prompt()
        
        system_prompt_content = None
        if use_system_prompt:
            system_prompt_content = read_system_prompt(question_dir)
            if system_prompt_content:
                print(f"已加载system prompt: {system_prompt_content[:50]}...")
            else:
                print("未找到system prompt文件，将不使用system prompt")
                use_system_prompt = False

        # Extract JSON responses
        print("正在提取时间最晚的md文档中的JSON响应...")
        test_json_responses = extract_json_responses(latest_md_file)
        print(f"从时间最晚的md文档中提取到 {len(test_json_responses)} 个JSON响应")

        print("正在提取answer.md文档中的JSON响应...")
        answer_json_responses = extract_json_responses(answer_file)
        print(f"从answer.md文档中提取到 {len(answer_json_responses)} 个JSON响应")

        # Check JSON response count consistency
        if len(test_json_responses) != len(answer_json_responses):
            print(f"警告：时间最晚的md文档和answer.md文档中的JSON响应数量不一致！")
            print(f"时间最晚的md文档: {len(test_json_responses)}, answer: {len(answer_json_responses)}")
            min_count = min(len(test_json_responses), len(answer_json_responses))
            test_json_responses = test_json_responses[:min_count]
            answer_json_responses = answer_json_responses[:min_count]
            print(f"将使用前 {min_count} 个响应进行处理")

        # Generate round2_response_without_images file path
        now = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        round2_response_without_images_file = os.path.join(round2_response_without_images_dir, f"{now}.md")
        output_lines = []

        # Write header - 预留准确率位置
        output_lines.append(f"# 运行时间: {now}\n\n")
        if grading_mode == "model":
            output_lines.append(f"**批改方式：** 模型交互\n\n")
            output_lines.append(f"**使用模型ID：** {selected_model}\n\n")
            output_lines.append(f"## 使用的prompt\n\n")
            output_lines.append(f"{round2_prompt_without_images_text}\n\n")
        else:
            output_lines.append(f"**批改方式：** JSON比对\n\n")
            output_lines.append(f"**比对说明：** 直接比对学生答案和正确答案的JSON字符串\n\n")

        # 处理JSON响应对
        if grading_mode == "model":
            print("\n--- 开始并行处理JSON响应对并与模型交互 ---\n")

            # Prepare tasks for parallel processing
            tasks = []
            for i, (test_json, answer_json) in enumerate(zip(test_json_responses, answer_json_responses), 1):
                tasks.append((test_json, answer_json, round2_prompt_without_images_text, i, api_key_from_client_init, selected_model, response_format, temperature, top_p, max_tokens, system_prompt_content if use_system_prompt else None))

            # Set the number of processes
            # 优化：使用线程池而不是进程池，并限制并发数量
            max_workers = min(8, os.cpu_count() if os.cpu_count() else 4)  # 限制最大8个并发
            print(f"将使用 {max_workers} 个线程进行并行处理（优化版）。")

            # 使用进度条显示API推理进度
            all_processed_results = []
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                with tqdm(total=len(tasks), desc="API推理", unit="组") as pbar:
                    # 更小的批次大小以降低API压力
                    batch_size = max(1, min(5, len(tasks) // max_workers))
                    for i in range(0, len(tasks), batch_size):
                        batch_tasks = tasks[i:i + batch_size]
                        # 使用starmap等效的方式处理参数
                        batch_results = list(executor.map(lambda args: process_single_json_pair_parallel(*args), batch_tasks))
                        all_processed_results.extend(batch_results)
                        pbar.update(len(batch_tasks))
                        # 添加小延迟以减轻API压力
                        time.sleep(0.1)

            print("\n--- 并行处理完成，合并结果 ---\n")
        else:
            print("\n--- 开始JSON比对处理 ---\n")

            all_processed_results = []
            for i, (test_json, answer_json) in enumerate(tqdm(zip(test_json_responses, answer_json_responses),
                                                              total=len(test_json_responses),
                                                              desc="JSON比对", unit="组"), 1):
                result = process_single_json_pair_compare(test_json, answer_json, i)
                all_processed_results.append(result)

            print("\n--- JSON比对处理完成 ---\n")

        # Collect results in order
        for result in all_processed_results:
            output_lines.extend(result['output_lines'])

        output_lines.append(f"\n{'='*50}\n")
        output_lines.append("所有JSON响应处理完成！\n")
        output_lines.append(f"{'='*50}\n")
        print(f"\n{'='*50}\n")
        print("所有JSON响应处理完成！")
        print(f"{'='*50}\n")

        # Write new content to the response file
        with open(round2_response_without_images_file, "w", encoding="utf-8") as f:
            f.writelines(output_lines)

        # If response_template.md doesn't exist, copy the current md file as a template
        template_path = os.path.join(round2_response_without_images_dir, "response_template.md")
        if not os.path.exists(template_path):
            import shutil
            shutil.copyfile(round2_response_without_images_file, template_path)
            print(f"已创建response_template.md模板文件")

        # Compare wrong questions functionality
        template_path = os.path.join(round2_response_without_images_dir, "response_template.md")
        wrong_items = []
        accuracy_info = None

        if os.path.exists(template_path):
            new_results = extract_json_responses_and_names(round2_response_without_images_file)
            template_results = extract_json_responses_and_names(template_path)
            wrongs = []
            error_msgs = []

            # Check response count consistency
            if len(new_results) != len(template_results):
                error_msgs.append(f"响应数量不一致：本次{len(new_results)}，模板{len(template_results)}")

            min_len = min(len(new_results), len(template_results))
            for i in range(min_len):
                new_name, new_json = new_results[i]
                tpl_name, tpl_json = template_results[i]
                if new_name != tpl_name:
                    wrongs.append(f"第 {i+1} 组响应: 响应名不一致，本次为 {new_name}，模板为 {tpl_name}")
                    wrong_items.append(i+1)
                elif new_json != tpl_json:
                    # JSON值序列比较
                    def compare_json_values(json_str1, json_str2):
                        try:
                            obj1 = json.loads(json_str1)
                            obj2 = json.loads(json_str2)

                            def extract_question_number(key):
                                import re
                                match = re.search(r'\d+', str(key))
                                if match:
                                    return int(match.group())
                                return 0

                            sorted_items1 = sorted(obj1.items(), key=lambda x: extract_question_number(x[0]))
                            sorted_items2 = sorted(obj2.items(), key=lambda x: extract_question_number(x[0]))

                            values1 = [item[1] for item in sorted_items1]
                            values2 = [item[1] for item in sorted_items2]

                            return values1 == values2
                        except:
                            # 安全处理可能为None的字符串
                            str1 = json_str1.strip() if json_str1 is not None else ""
                            str2 = json_str2.strip() if json_str2 is not None else ""
                            return str1 == str2

                    if not compare_json_values(new_json, tpl_json):
                        wrongs.append(f"第 {i+1} 组响应: {new_name}")
                        wrong_items.append(i+1)

            # Calculate accuracy
            total = min_len
            wrong_count = len(wrongs)
            accuracy = (total - wrong_count) / total if total > 0 else 1.0
            accuracy_str = f"准确率：{(accuracy*100):.2f}%  （({total} - {wrong_count}) / {total}）\n"

            # 保存准确率信息供返回使用
            accuracy_info = {
                'accuracy': accuracy,
                'accuracy_str': accuracy_str.strip(),
                'total': total,
                'wrong_count': wrong_count,
                'accuracy_percentage': f"{(accuracy*100):.2f}%"
            }

            # 更新文件内容，将准确率置顶
            new_top = f"## {accuracy_str}\n"
            new_top += "## 错题\n"
            if error_msgs:
                for msg in error_msgs:
                    new_top += f"- {msg}\n"
            if wrongs:
                for w in wrongs:
                    new_top += f"- {w}\n"
            if not error_msgs and not wrongs:
                new_top += "本次无错题。\n"
            new_top += "\n"

            # Read old content and write new content
            with open(round2_response_without_images_file, "r", encoding="utf-8") as f:
                old_content = f.read()

            with open(round2_response_without_images_file, "w", encoding="utf-8") as f:
                f.write(new_top)
                f.write(old_content)

            # 控制台输出（只打印准确率，不打印详细错题列表）
            console_output = f"## {accuracy_str}\n"
            if not error_msgs and not wrongs:
                console_output += "## 错题\n本次无错题。\n"
            else:
                console_output += f"## 错题\n共 {len(wrongs) + len(error_msgs)} 项错题（详细信息已保存到文件）\n"
            print(console_output)

            # 创建错题汇总文件 - 添加缺失的逻辑，包含完整的4个JSON部分
            if wrong_items:
                # 确保 round2_error_without_images 目录存在
                round2_error_without_images_dir = os.path.join(question_dir, "round2_error_without_images")
                os.makedirs(round2_error_without_images_dir, exist_ok=True)

                current_date = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
                summary_file = os.path.join(round2_error_without_images_dir, f"error_summary_{current_date}.md")

                # 提取学生答案和正确答案数据
                test_json_responses = extract_json_responses(latest_md_file)
                answer_json_responses = extract_json_responses(answer_file)

                summary_lines = []

                # 头部结构：准确率、运行时间置顶
                summary_lines.append(f"## 准确率：{(accuracy*100):.2f}%  （({total} - {wrong_count}) / {total}）\n\n")
                summary_lines.append(f"## 运行时间: {current_date}\n\n")

                # 添加模型ID信息（如果是模型交互模式）
                if grading_mode == "model":
                    summary_lines.append(f"**使用模型ID：** {selected_model}\n\n")
                    summary_lines.append(f"**批改方式：** 模型交互\n\n")
                    summary_lines.append(f"## 使用的prompt\n\n{round2_prompt_without_images_text}\n\n")
                else:
                    summary_lines.append(f"**批改方式：** JSON比对\n\n")
                    summary_lines.append(f"**比对说明：** 直接比对学生答案和正确答案的JSON字符串\n\n")

                # 错题列表
                summary_lines.append(f"## 错题\n\n")
                for item in wrong_items:
                    summary_lines.append(f"- 第 {item} 组响应\n")

                # 插入错题的详细内容，展示四个JSON（与test3.py保持一致）
                for idx in wrong_items:
                    # 获取四个JSON
                    student_json = test_json_responses[idx-1] if idx <= len(test_json_responses) else '{}'
                    correct_json = answer_json_responses[idx-1] if idx <= len(answer_json_responses) else '{}'
                    template_json = template_results[idx-1][1] if idx <= len(template_results) else '{}'
                    response_json = new_results[idx-1][1] if idx <= len(new_results) else '{}'

                    # 构建错题内容
                    summary_lines.append(f"\n==================================================\n")
                    summary_lines.append(f"处理第 {idx} 组JSON响应\n")
                    summary_lines.append(f"==================================================\n")

                    # 1. 学生答案
                    summary_lines.append("### 学生答案：\n")
                    summary_lines.append("```json\n")
                    summary_lines.append(f"{student_json}\n")
                    summary_lines.append("```\n\n")

                    # 2. 正确答案
                    summary_lines.append("### 正确答案：\n")
                    summary_lines.append("```json\n")
                    summary_lines.append(f"{correct_json}\n")
                    summary_lines.append("```\n\n")

                    # 3. response_template答案（模板答案）
                    summary_lines.append("### response_template答案：\n")
                    summary_lines.append("```json\n")
                    summary_lines.append(f"{template_json}\n")
                    summary_lines.append("```\n\n")

                    # 4. 响应内容（本次生成的）
                    summary_lines.append("### 响应内容：\n")
                    summary_lines.append("```json\n")
                    summary_lines.append(f"{response_json}\n")
                    summary_lines.append("```\n")

                summary_lines.append("\n==================================================\n所有错题处理完成！\n==================================================\n")

                with open(summary_file, "w", encoding="utf-8") as f:
                    f.writelines(summary_lines)

                print(f"已创建错题详细 summary.md 文件: {summary_file}")

        print(f"结果已保存到：{round2_response_without_images_file}")

        # 返回准确率信息
        if accuracy_info:
            return True, accuracy_info
        else:
            # 如果没有模板文件，返回默认准确率信息
            default_accuracy_info = {
                'accuracy': 0.0,
                'accuracy_str': '准确率：未知（无模板文件）',
                'total': 0,
                'wrong_count': 0,
                'accuracy_percentage': '未知'
            }
            return True, default_accuracy_info

    except Exception as e:
        print(f"test2执行异常: {e}")
        error_accuracy_info = {
            'accuracy': 0.0,
            'accuracy_str': f'准确率：执行异常 - {e}',
            'total': 0,
            'wrong_count': 0,
            'accuracy_percentage': '异常'
        }
        return False, error_accuracy_info

def run_test2_with_config(config_file):
    """
    使用配置文件运行test2，供main.py调用
    """
    import json

    # 加载配置
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"错误：无法加载配置文件 {config_file}: {e}")
        default_accuracy_info = {
            'accuracy': 0.0,
            'accuracy_str': '准确率：未知（配置文件加载失败）',
            'total': 0,
            'wrong_count': 0,
            'accuracy_percentage': '未知'
        }
        return False, default_accuracy_info

    # 从配置中提取参数
    grading_mode = config.get('grading_mode', 'json_compare')
    selected_model = config.get('model_id', 'doubao-seed-1-6-flash-250715')
    response_format = config.get('response_format', 'text')
    question_type = config.get('question_type', '涂卡选择题')
    pinyin_name = config.get('pinyin_name', 'tukaxuanzeti')
    custom_prompt = config.get('test2_prompt')
    temperature = config.get('temperature', 1)
    top_p = config.get('top_p', 0.7)
    max_tokens = config.get('max_tokens', 4096)
    use_system_prompt = config.get('use_system_prompt', False)

    try:
        # 执行test2的主要逻辑
        api_key_from_client_init = "36c2aa0e-8b2b-4412-bc92-d3d1cef96b1b"

        # Build type-related paths
        types_dir = "types"
        question_dir = os.path.join(types_dir, pinyin_name)
        response_dir = os.path.join(question_dir, "response")
        round2_response_without_images_dir = os.path.join(question_dir, "round2_response_without_images")
        round2_prompt_without_images_file = os.path.join(question_dir, "round2_prompt_without_images.md")
        answer_file = os.path.join(response_dir, "answer.md")

        # Create round2_response_without_images folder
        os.makedirs(round2_response_without_images_dir, exist_ok=True)

        # Check for necessary files
        if not custom_prompt and not os.path.exists(round2_prompt_without_images_file):
            print(f"错误：round2_prompt_without_images文件 {round2_prompt_without_images_file} 不存在！")
            error_accuracy_info = {
                'accuracy': 0.0,
                'accuracy_str': '准确率：缺少提示词文件',
                'total': 0,
                'wrong_count': 0,
                'accuracy_percentage': '错误'
            }
            return False, error_accuracy_info

        if not os.path.exists(answer_file):
            print(f"错误：answer文件 {answer_file} 不存在！")
            error_accuracy_info = {
                'accuracy': 0.0,
                'accuracy_str': '准确率：缺少答案文件',
                'total': 0,
                'wrong_count': 0,
                'accuracy_percentage': '错误'
            }
            return False, error_accuracy_info

        # 调用实际的test2处理逻辑
        return execute_test2_logic(
            grading_mode, selected_model, response_format, question_type, pinyin_name,
            custom_prompt, temperature, top_p, max_tokens, api_key_from_client_init,
            types_dir, question_dir, response_dir, round2_response_without_images_dir,
            round2_prompt_without_images_file, answer_file, use_system_prompt
        )

    except Exception as e:
        print(f"test2执行异常: {e}")
        error_accuracy_info = {
            'accuracy': 0.0,
            'accuracy_str': f'准确率：执行异常 - {e}',
            'total': 0,
            'wrong_count': 0,
            'accuracy_percentage': '异常'
        }
        return False, error_accuracy_info

# 清理函数：关闭HTTP客户端连接池
def cleanup_http_client():
    """清理HTTP客户端连接池"""
    try:
        HTTP_CLIENT.close()
        print("HTTP客户端连接池已关闭")
    except Exception as e:
        print(f"关闭HTTP客户端时出错: {e}")

# 程序退出时自动清理
import atexit
atexit.register(cleanup_http_client)